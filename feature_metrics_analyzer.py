#!/usr/bin/env python3
"""
Feature Metrics Analysis Script for iOS Repository
Extracts comprehensive feature development metrics from git history
"""

import re
import csv
import subprocess
import json
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import os
import sys

class FeatureMetricsAnalyzer:
    def __init__(self, repo_path="."):
        self.repo_path = repo_path
        self.jira_pattern = re.compile(r'(?i)([A-Z]{2,10}-\d+)')
        self.features = {}
        self.total_commits_analyzed = 0
        
    def run_git_command(self, command):
        """Execute git command and return output"""
        try:
            result = subprocess.run(
                command, shell=True, cwd=self.repo_path,
                capture_output=True, text=True, check=True
            )
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            print(f"Git command failed: {command}")
            print(f"Error: {e.stderr}")
            return ""
    
    def validate_repository_completeness(self):
        """Validate that we have complete repository history"""
        # Check if repository is shallow
        is_shallow = self.run_git_command("git rev-parse --is-shallow-repository")
        if is_shallow == "true":
            print("ERROR: Repository is shallow. Run 'git fetch --unshallow' first.")
            sys.exit(1)
        
        # Count total commits
        total_commits = self.run_git_command("git rev-list --all | wc -l")
        self.total_commits_analyzed = int(total_commits)
        print(f"✅ Repository validation: {self.total_commits_analyzed} total commits")
        
        return True
    
    def extract_all_branches(self):
        """Extract all branches including deleted ones from reflog"""
        branches = set()
        
        # Get current remote branches
        remote_branches = self.run_git_command("git branch -r").split('\n')
        for branch in remote_branches:
            branch = branch.strip()
            if branch and not branch.startswith('origin/HEAD'):
                branches.add(branch.replace('origin/', ''))
        
        # Get branches from reflog (including deleted ones)
        reflog_output = self.run_git_command("git reflog show --all")
        for line in reflog_output.split('\n'):
            if 'checkout: moving from' in line:
                parts = line.split('checkout: moving from')
                if len(parts) > 1:
                    branch_info = parts[1].strip()
                    # Extract branch names
                    branch_parts = branch_info.split(' to ')
                    if len(branch_parts) >= 2:
                        from_branch = branch_parts[0].strip()
                        to_branch = branch_parts[1].strip()
                        branches.add(from_branch)
                        branches.add(to_branch)
        
        return list(branches)
    
    def extract_jira_tickets_from_commits(self):
        """Extract all Jira tickets from commit messages"""
        jira_tickets = set()
        
        # Get all commit messages
        commit_messages = self.run_git_command(
            "git log --all --pretty=format:'%s'"
        ).split('\n')
        
        for message in commit_messages:
            matches = self.jira_pattern.findall(message)
            for match in matches:
                jira_tickets.add(match.upper())
        
        return list(jira_tickets)
    
    def get_feature_branch_info(self, branch_name):
        """Get detailed information about a feature branch"""
        # Extract Jira ticket from branch name
        jira_match = self.jira_pattern.search(branch_name)
        jira_ticket = jira_match.group(1).upper() if jira_match else None
        
        # Get branch creation and last commit dates
        try:
            # Get first commit in branch
            first_commit = self.run_git_command(
                f"git log --reverse --pretty=format:'%H|%ai|%an|%ae|%s' origin/{branch_name} | head -1"
            )
            
            # Get last commit in branch
            last_commit = self.run_git_command(
                f"git log --pretty=format:'%H|%ai|%an|%ae|%s' origin/{branch_name} | head -1"
            )
            
            if not first_commit or not last_commit:
                return None
                
            first_parts = first_commit.split('|')
            last_parts = last_commit.split('|')
            
            if len(first_parts) < 5 or len(last_parts) < 5:
                return None
            
            # Get commit statistics
            stats = self.run_git_command(
                f"git log --numstat --pretty=format:'' origin/{branch_name}"
            )
            
            # Parse statistics
            lines_added = 0
            lines_deleted = 0
            files_changed = set()
            
            for line in stats.split('\n'):
                if line.strip() and '\t' in line:
                    parts = line.split('\t')
                    if len(parts) >= 3:
                        try:
                            added = int(parts[0]) if parts[0] != '-' else 0
                            deleted = int(parts[1]) if parts[1] != '-' else 0
                            filename = parts[2]
                            
                            lines_added += added
                            lines_deleted += deleted
                            files_changed.add(filename)
                        except ValueError:
                            continue
            
            # Get commit count
            commit_count = len(self.run_git_command(
                f"git log --oneline origin/{branch_name}"
            ).split('\n'))
            
            # Get unique authors
            authors = set()
            author_commits = self.run_git_command(
                f"git log --pretty=format:'%an' origin/{branch_name}"
            ).split('\n')
            for author in author_commits:
                if author.strip():
                    authors.add(author.strip())
            
            return {
                'jira_ticket': jira_ticket,
                'branch_name': branch_name,
                'development_start_date': first_parts[1],
                'development_end_date': last_parts[1],
                'lines_added': lines_added,
                'lines_deleted': lines_deleted,
                'files_changed': len(files_changed),
                'commits_count': commit_count,
                'team_size': len(authors),
                'developer_names': list(authors),
                'feature_description': last_parts[4]
            }
            
        except Exception as e:
            print(f"Error processing branch {branch_name}: {e}")
            return None
    
    def calculate_development_metrics(self, feature_info):
        """Calculate additional development metrics"""
        if not feature_info:
            return feature_info
        
        try:
            start_date = datetime.fromisoformat(feature_info['development_start_date'].replace('Z', '+00:00'))
            end_date = datetime.fromisoformat(feature_info['development_end_date'].replace('Z', '+00:00'))
            
            total_dev_days = (end_date - start_date).days
            
            # Calculate complexity score (simple heuristic)
            complexity_score = (
                feature_info['lines_added'] * 0.1 +
                feature_info['lines_deleted'] * 0.05 +
                feature_info['files_changed'] * 2 +
                feature_info['commits_count'] * 1
            )
            
            # Calculate productivity factor
            productivity_factor = (
                6 * (feature_info['team_size'] * total_dev_days) +
                4 * 0  # post_release_fixes (to be calculated later)
            )
            
            feature_info.update({
                'total_dev_days': max(total_dev_days, 1),  # Minimum 1 day
                'complexity_score': round(complexity_score, 2),
                'productivity_factor': round(productivity_factor, 2),
                'lines_modified': feature_info['lines_added'] + feature_info['lines_deleted'],
                'total_line_count': feature_info['lines_added'] - feature_info['lines_deleted']
            })
            
        except Exception as e:
            print(f"Error calculating metrics for {feature_info.get('jira_ticket', 'unknown')}: {e}")
        
        return feature_info
    
    def identify_module_component(self, feature_info):
        """Identify the primary module/component affected"""
        if not feature_info:
            return feature_info
        
        # Get files changed in the feature
        try:
            files_output = self.run_git_command(
                f"git log --name-only --pretty=format:'' origin/{feature_info['branch_name']}"
            )
            
            files = [f.strip() for f in files_output.split('\n') if f.strip()]
            
            # Analyze file paths to determine module
            module_counts = Counter()
            
            for file_path in files:
                if 'Atlantida/Source/Modules/' in file_path:
                    # Extract module name
                    parts = file_path.split('Atlantida/Source/Modules/')
                    if len(parts) > 1:
                        module = parts[1].split('/')[0]
                        module_counts[module] += 1
                elif 'Atlantida/Source/Components/' in file_path:
                    module_counts['Components'] += 1
                elif 'Atlantida/Source/Core/' in file_path:
                    module_counts['Core'] += 1
                elif 'Atlantida/Source/DesignSystem/' in file_path:
                    module_counts['DesignSystem'] += 1
                else:
                    module_counts['Other'] += 1
            
            # Get the most affected module
            primary_module = module_counts.most_common(1)[0][0] if module_counts else 'Unknown'
            feature_info['module_component'] = primary_module
            
        except Exception as e:
            print(f"Error identifying module for {feature_info.get('jira_ticket', 'unknown')}: {e}")
            feature_info['module_component'] = 'Unknown'
        
        return feature_info

    def analyze_all_features(self):
        """Main method to analyze all features in the repository"""
        print("🔍 Starting comprehensive feature analysis...")

        # Validate repository
        self.validate_repository_completeness()

        # Get all branches
        all_branches = self.extract_all_branches()
        feature_branches = [b for b in all_branches if any(
            pattern in b.lower() for pattern in ['feature/', 'bugfix/', 'hotfix/']
        )]

        print(f"📊 Found {len(feature_branches)} feature/bugfix/hotfix branches")

        # Process each feature branch
        processed_features = []

        for i, branch in enumerate(feature_branches, 1):
            print(f"Processing {i}/{len(feature_branches)}: {branch}")

            feature_info = self.get_feature_branch_info(branch)
            if feature_info:
                feature_info = self.calculate_development_metrics(feature_info)
                feature_info = self.identify_module_component(feature_info)
                processed_features.append(feature_info)

        # Also process commits with Jira tickets that might not have branches
        jira_tickets = self.extract_jira_tickets_from_commits()
        branch_jira_tickets = {f.get('jira_ticket') for f in processed_features if f.get('jira_ticket')}

        orphaned_tickets = set(jira_tickets) - branch_jira_tickets
        print(f"🔍 Found {len(orphaned_tickets)} orphaned Jira tickets without branches")

        # Process orphaned tickets (limit to avoid overwhelming)
        for ticket in list(orphaned_tickets)[:20]:  # Process first 20 orphaned tickets
            orphaned_feature = self.process_orphaned_jira_ticket(ticket)
            if orphaned_feature:
                processed_features.append(orphaned_feature)

        self.features = processed_features
        print(f"✅ Analysis complete: {len(processed_features)} features processed")

        return processed_features

    def process_orphaned_jira_ticket(self, jira_ticket):
        """Process Jira tickets that don't have dedicated branches"""
        try:
            # Find all commits with this Jira ticket
            commits_output = self.run_git_command(
                f"git log --all --grep='{jira_ticket}' --pretty=format:'%H|%ai|%an|%ae|%s'"
            )

            if not commits_output:
                return None

            commits = commits_output.split('\n')
            if not commits:
                return None

            # Get first and last commit
            first_commit = commits[-1].split('|')  # Oldest commit
            last_commit = commits[0].split('|')    # Newest commit

            if len(first_commit) < 5 or len(last_commit) < 5:
                return None

            # Get statistics for all commits with this ticket
            commit_hashes = [c.split('|')[0] for c in commits if c.strip()]

            lines_added = 0
            lines_deleted = 0
            files_changed = set()
            authors = set()

            for commit_hash in commit_hashes:
                # Get numstat for this commit
                stats = self.run_git_command(f"git show --numstat {commit_hash}")

                for line in stats.split('\n'):
                    if line.strip() and '\t' in line:
                        parts = line.split('\t')
                        if len(parts) >= 3:
                            try:
                                added = int(parts[0]) if parts[0] != '-' else 0
                                deleted = int(parts[1]) if parts[1] != '-' else 0
                                filename = parts[2]

                                lines_added += added
                                lines_deleted += deleted
                                files_changed.add(filename)
                            except ValueError:
                                continue

                # Get author
                author = self.run_git_command(f"git show --pretty=format:'%an' {commit_hash} | head -1")
                if author:
                    authors.add(author)

            # Calculate development time
            start_date = datetime.fromisoformat(first_commit[1].replace('Z', '+00:00'))
            end_date = datetime.fromisoformat(last_commit[1].replace('Z', '+00:00'))
            total_dev_days = max((end_date - start_date).days, 1)

            # Calculate complexity score
            complexity_score = (
                lines_added * 0.1 +
                lines_deleted * 0.05 +
                len(files_changed) * 2 +
                len(commits) * 1
            )

            return {
                'jira_ticket': jira_ticket,
                'branch_name': f'orphaned/{jira_ticket}',
                'development_start_date': first_commit[1],
                'development_end_date': last_commit[1],
                'total_dev_days': total_dev_days,
                'lines_added': lines_added,
                'lines_deleted': lines_deleted,
                'lines_modified': lines_added + lines_deleted,
                'total_line_count': lines_added - lines_deleted,
                'files_changed': len(files_changed),
                'commits_count': len(commits),
                'complexity_score': round(complexity_score, 2),
                'team_size': len(authors),
                'developer_names': list(authors),
                'feature_description': last_commit[4],
                'module_component': 'Unknown',
                'productivity_factor': round(6 * (len(authors) * total_dev_days), 2)
            }

        except Exception as e:
            print(f"Error processing orphaned ticket {jira_ticket}: {e}")
            return None

    def export_to_csv(self, filename='feature_metrics_analysis.csv'):
        """Export feature metrics to CSV"""
        if not self.features:
            print("No features to export")
            return

        fieldnames = [
            'jira_ticket', 'feature_description', 'module_component',
            'development_start_date', 'development_end_date', 'total_dev_days',
            'lines_added', 'lines_deleted', 'lines_modified', 'total_line_count',
            'files_changed', 'commits_count', 'branch_name', 'complexity_score',
            'team_size', 'developer_names', 'productivity_factor'
        ]

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for feature in self.features:
                # Convert developer_names list to string
                feature_copy = feature.copy()
                feature_copy['developer_names'] = '; '.join(feature.get('developer_names', []))

                # Ensure all required fields exist
                for field in fieldnames:
                    if field not in feature_copy:
                        feature_copy[field] = ''

                writer.writerow(feature_copy)

        print(f"✅ CSV exported: {filename} ({len(self.features)} features)")

    def generate_summary_report(self, filename='repository_analysis_summary.md'):
        """Generate summary analysis report"""
        if not self.features:
            print("No features to analyze")
            return

        # Calculate summary statistics
        total_features = len(self.features)
        avg_dev_days = sum(f.get('total_dev_days', 0) for f in self.features) / total_features
        avg_complexity = sum(f.get('complexity_score', 0) for f in self.features) / total_features
        avg_team_size = sum(f.get('team_size', 0) for f in self.features) / total_features

        # Module distribution
        modules = Counter(f.get('module_component', 'Unknown') for f in self.features)

        # Developer contribution
        all_developers = []
        for f in self.features:
            all_developers.extend(f.get('developer_names', []))
        developer_counts = Counter(all_developers)

        # Generate report
        report = f"""# Repository Analysis Summary

## Overview
- **Total Features Analyzed**: {total_features}
- **Total Commits Analyzed**: {self.total_commits_analyzed}
- **Analysis Date**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Development Metrics
- **Average Development Time**: {avg_dev_days:.1f} days
- **Average Complexity Score**: {avg_complexity:.1f}
- **Average Team Size**: {avg_team_size:.1f} developers

## Module Distribution
"""

        for module, count in modules.most_common():
            percentage = (count / total_features) * 100
            report += f"- **{module}**: {count} features ({percentage:.1f}%)\n"

        report += f"""
## Top Contributors
"""

        for developer, count in developer_counts.most_common(10):
            report += f"- **{developer}**: {count} features\n"

        # Feature size distribution
        small_features = len([f for f in self.features if f.get('total_dev_days', 0) <= 3])
        medium_features = len([f for f in self.features if 3 < f.get('total_dev_days', 0) <= 14])
        large_features = len([f for f in self.features if f.get('total_dev_days', 0) > 14])

        report += f"""
## Feature Size Distribution
- **Small Features** (≤3 days): {small_features} ({(small_features/total_features)*100:.1f}%)
- **Medium Features** (4-14 days): {medium_features} ({(medium_features/total_features)*100:.1f}%)
- **Large Features** (>14 days): {large_features} ({(large_features/total_features)*100:.1f}%)

## Repository Structure
- **Primary Language**: Swift (iOS)
- **Architecture**: Modular SwiftUI/UIKit
- **Main Modules**: {', '.join([m for m, _ in modules.most_common(5)])}
"""

        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report)

        print(f"✅ Summary report generated: {filename}")

    def generate_estimation_recommendations(self, filename='feature_estimation_recommendations.md'):
        """Generate estimation recommendations based on analysis"""
        if not self.features:
            print("No features to analyze")
            return

        # Calculate percentiles for estimation
        dev_days = [f.get('total_dev_days', 0) for f in self.features if f.get('total_dev_days', 0) > 0]
        complexity_scores = [f.get('complexity_score', 0) for f in self.features]

        dev_days.sort()
        complexity_scores.sort()

        def percentile(data, p):
            if not data:
                return 0
            k = (len(data) - 1) * p / 100
            f = int(k)
            c = k - f
            if f == len(data) - 1:
                return data[f]
            return data[f] * (1 - c) + data[f + 1] * c

        recommendations = f"""# Feature Estimation Recommendations

## Development Time Estimates

### By Percentile
- **50th percentile (Typical)**: {percentile(dev_days, 50):.1f} days
- **75th percentile (Conservative)**: {percentile(dev_days, 75):.1f} days
- **90th percentile (High Risk)**: {percentile(dev_days, 90):.1f} days

### By Feature Size
- **Small Features**: 1-3 days (simple UI changes, bug fixes)
- **Medium Features**: 4-14 days (new screens, integrations)
- **Large Features**: 15+ days (complex flows, major refactoring)

## Complexity Factors
- **Low Complexity** (<{percentile(complexity_scores, 33):.1f}): Simple changes, minimal files
- **Medium Complexity** ({percentile(complexity_scores, 33):.1f}-{percentile(complexity_scores, 67):.1f}): Standard features
- **High Complexity** (>{percentile(complexity_scores, 67):.1f}): Complex integrations, many files

## Team Productivity
- **Average Team Size**: {sum(f.get('team_size', 0) for f in self.features) / len(self.features):.1f} developers
- **Recommended Team Size**: 1-2 developers for most features
- **Large Feature Team Size**: 2-3 developers maximum

## Risk Factors
1. **Features >21 days**: High risk of scope creep
2. **High file count** (>20 files): Integration complexity
3. **Multiple modules**: Cross-team coordination needed

## Module-Specific Estimates
"""

        # Module-specific analysis
        module_stats = defaultdict(list)
        for feature in self.features:
            module = feature.get('module_component', 'Unknown')
            dev_days = feature.get('total_dev_days', 0)
            if dev_days > 0:
                module_stats[module].append(dev_days)

        for module, days_list in module_stats.items():
            if len(days_list) >= 3:  # Only include modules with enough data
                avg_days = sum(days_list) / len(days_list)
                recommendations += f"- **{module}**: {avg_days:.1f} days average ({len(days_list)} features)\n"

        with open(filename, 'w', encoding='utf-8') as f:
            f.write(recommendations)

        print(f"✅ Estimation recommendations generated: {filename}")

def main():
    """Main execution function"""
    print("🚀 Starting Feature Metrics Analysis")
    print("=" * 50)

    analyzer = FeatureMetricsAnalyzer()

    # Run complete analysis
    features = analyzer.analyze_all_features()

    if not features:
        print("❌ No features found to analyze")
        return

    # Export results
    analyzer.export_to_csv()
    analyzer.generate_summary_report()
    analyzer.generate_estimation_recommendations()

    print("=" * 50)
    print("✅ Analysis complete! Generated files:")
    print("  - feature_metrics_analysis.csv")
    print("  - repository_analysis_summary.md")
    print("  - feature_estimation_recommendations.md")

if __name__ == "__main__":
    main()
