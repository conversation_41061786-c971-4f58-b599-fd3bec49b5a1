jira_ticket,feature_description,module_component,development_start_date,development_end_date,total_dev_days,lines_added,lines_deleted,lines_modified,total_line_count,files_changed,commits_count,branch_name,complexity_score,team_size,developer_names,productivity_factor
ONE-1417,🚧 ONE-1417 - Combine service calls,TB-CreditCardMenu,2025-06-10T08:59:01-06:00,2025-06-10T08:59:01-06:00,1,69,7,76,62,2,1,bugfix/ONE-1417,12.25,1,<PERSON>,6
ONE-2222,💄 ONE-2222 - Fix visual issues,Main,2025-06-10T10:06:29-06:00,2025-06-10T14:46:59-06:00,1,134,50,184,84,5,3,bugfix/ONE-2222,28.9,1,<PERSON>,6
ONE-2324,Merged develop into bugfix/ONE-2324,AC-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>tion,2025-09-18T15:35:06-06:00,2025-09-19T17:36:39+00:00,1,9,9,18,0,3,4,bugfix/ONE-2324,11.35,1,<PERSON>,6
ONE-2627,🔥 ONE-2627 - Update padding spacing and scroll area.,TB-<PERSON>u,2025-07-15T14:58:45-06:00,2025-07-15T14:58:45-06:00,1,20,20,40,0,1,1,bugfix/ONE-2627,6.0,1,Julio <PERSON>,6
ONE-2627,💄ONE-2627 - Move padding to computed property `header` and `invitedMessage`.,TB-Menu,2025-07-16T10:23:49-06:00,2025-07-17T11:25:27-06:00,1,48,45,93,3,4,4,bugfix/ONE-2627-update,19.05,3,Julio Rico; Emely Melgar; Jenkins CI,18
ONE-2627,💄ONE-2627 - Update padding spacing and scroll area.,TB-Menu,2025-07-18T11:39:09-06:00,2025-07-18T11:39:09-06:00,1,35,31,66,4,1,1,bugfix/ONE-2627-v2,8.05,1,Julio Rico,6
ONE-2629,"🐛 ONE-2629 - Reformat validation, reset is handle on VM",AC-UserAuthentication,2025-07-23T18:05:18-06:00,2025-07-25T14:52:36-06:00,1,90,73,163,17,4,12,bugfix/ONE-2629,32.65,2,Javier Lorenzana; Jenkins CI,12
ONE-2797,🐛 ONE-2797 - Fix card delivery summary UI,Main,2025-07-25T11:21:01-06:00,2025-07-25T11:21:01-06:00,1,159,30,189,129,6,1,bugfix/ONE-2797,30.4,1,Rodrigo Mejia,6
ONE-2797,🐛 ONE-2797 - Fix missing UI update on additional card/third attempt flows,Main,2025-07-29T17:32:30-06:00,2025-07-29T17:32:30-06:00,1,143,30,173,113,7,1,bugfix/ONE-2797-v3,30.8,1,Rodrigo Mejia,6
ONE-3138,🐛 ONE-3138 - add space after filter,TB-CreditCardMenu,2025-09-17T11:21:22-06:00,2025-09-17T13:23:11-06:00,1,6,5,11,1,2,4,bugfix/ONE-3138,8.85,1,Javier Lorenzana,6
ONE-3138,🐛 ONE-3138v1 - replace published variable to a computed variable with filters and validations.,TB-CreditCardMenu,2025-09-17T15:14:42-06:00,2025-09-22T10:45:39-06:00,4,28,28,56,0,2,8,bugfix/ONE-3138v1,16.2,1,Javier Lorenzana,24
ONE-3259,💄 ONE-3259 - Resize close button and prevent scroll,Main,2025-10-06T13:06:18-06:00,2025-10-06T13:06:18-06:00,1,2,2,4,0,1,1,bugfix/ONE-3259,3.3,1,Rodrigo Mejia,6
ONE-2046,♻️ ONE-2046 - Refactor and reorganize component `HomeView` and `HomeCardDetailView` to apply shimmering effect,TB-Home,2025-04-28T16:05:24-06:00,2025-04-28T16:06:27-06:00,1,202,47,249,155,6,2,feature/ONE-2046,36.55,1,Julio Rico,6
ONE-2259,♻️ ONE-2259 - Include CustomPageIndicatorStyleConfiguration.,AC-UserAuthentication,2025-06-06T15:28:08-06:00,2025-09-30T15:47:43-06:00,116,4320,746,5066,3574,61,45,feature/ONE-2259-Part2,636.3,1,Emely Melgar,696
ONE-2348,✅ ONE-2348 - Fix broken unit tests,TB-Home,2025-05-28T18:08:13-06:00,2025-06-09T23:08:51-06:00,12,2391,1607,3998,784,113,34,feature/ONE-2348,579.45,1,Josseh Blanco,72
ONE-2376,♻️ ONE-2376 - Refactor Login Response Data,Core,2025-05-29T08:30:51-06:00,2025-06-04T16:15:26-06:00,6,92,96,188,-4,3,4,feature/ONE-2376,24.0,1,Miguel Rivera,36
ONE-2377,♻️ ONE-2377 - Unify the Profile and GetLastLogin Endpoint,Core,2025-06-04T11:00:47-06:00,2025-06-04T11:00:47-06:00,1,48,41,89,7,6,1,feature/ONE-2377,19.85,1,Miguel Rivera,6
ONE-2378,♻️ ONE-2378 - Refactor Login Response Data,Core,2025-05-29T15:45:13-06:00,2025-06-04T16:55:11-06:00,6,135,137,272,-2,7,3,feature/ONE-2378,37.35,1,Miguel Rivera,36
ONE-2522,🎨 ONE-2522 - Add final network connection logic proposal,Core,2025-06-25T14:53:06-06:00,2025-07-14T15:41:56-06:00,19,847,209,1056,638,15,8,feature/ONE-2522,133.15,1,Rodrigo Mejia,114
ONE-2579,🎨 ONE-2579 - Remove extra line,Main,2025-07-15T15:27:35-06:00,2025-07-23T15:30:30-06:00,8,1275,865,2140,410,30,26,feature/ONE-2579,256.75,1,Rodrigo Mejia,48
ONE-2768,🚧 ONE-2768 - Add last files,Swift Code,2025-07-29T08:18:55-06:00,2025-08-07T17:39:19-06:00,9,214,47,261,167,16,5,feature/ONE-2768,60.75,1,Javier Lorenzana,54
ONE-2821,✏️ ONE-2821 - Fix typo,Main,2025-08-12T16:38:03-06:00,2025-08-12T16:39:06-06:00,1,37,7,44,30,3,3,feature/ONE-2821,13.05,1,Rodrigo Mejia,6
ONE-2901,🚧 ONE-2901 - Include row builder.,Main,2025-09-22T12:22:32-06:00,2025-09-23T15:27:57-06:00,1,290,30,320,260,8,3,feature/ONE-2901,49.5,1,Emely Melgar,6
ONE-3018,🔀 Merge branch 'develop' into feature/ONE-3018,DesignSystem,2025-09-12T08:31:49-06:00,2025-09-22T14:09:35-06:00,10,80,72,152,8,27,7,feature/ONE-3018,72.6,2,Javier Lorenzana; Miguel Rivera,120
ONE-3020,Merged develop into feature/ONE-3020,TB-Home,2025-09-17T16:10:18-06:00,2025-09-23T23:55:32+00:00,6,1120,470,1590,650,48,19,feature/ONE-3020,250.5,1,Rodrigo Mejia,36
ONE-3058,💚 ONE-3058 - Upload XML report dependency check,Other,2025-09-23T14:21:20-06:00,2025-09-23T14:21:20-06:00,1,1,1,2,0,1,1,feature/ONE-3058-v2,3.15,1,Emilio Vasquez,6
ONE-3158,Merge remote-tracking branch 'origin/develop' into feature/ONE-3158,Other,2025-10-01T08:27:23-06:00,2025-10-06T15:04:30-06:00,5,611,3,614,608,28,3,feature/ONE-3158,120.25,1,Miguel Rivera,30
ONE-3169,🌐 ONE-3169 - Create internationalization for CreditCardStyle,Components,2025-09-24T20:29:58-06:00,2025-09-30T10:24:46-06:00,5,55,42,97,13,4,8,feature/ONE-3169,23.6,1,Javier Lorenzana,30
ONE-3170,🌐 ONE-3170 - Add some missing default errors,Components,2025-09-25T14:41:31-06:00,2025-10-03T22:07:02-06:00,8,1057,91,1148,966,35,11,feature/ONE-3170,191.25,1,Emilio Vasquez,48
ONE-3188,♻️ONE-3188 - Rename `OneCardPinCheckpointHandler` to `OneCardPinCheckpointInteractor`.,Main,2025-10-01T14:29:02-06:00,2025-10-06T16:59:07-06:00,5,584,221,805,363,40,24,feature/ONE-3188,173.45,1,Julio Rico,30
ONE-3189,Merge branch 'develop' into feature/ONE-3189,TB-CreditCardMenu,2025-09-30T09:47:13-06:00,2025-10-06T15:50:40-06:00,6,1224,420,1644,804,104,18,feature/ONE-3189,369.4,1,Josseh Blanco,36
,🎨 ONE-AddGithubReviewers - Improve pull request template,Other,2025-09-25T12:08:39-06:00,2025-09-25T17:02:43-06:00,1,37,15,52,22,2,4,feature/ONE-AddGithubReviewers,12.45,1,Emilio Vasquez,6
PIPELINE-2,PR to test the pipeline trigger,Configuration,2025-07-04T08:15:34-03:00,2025-07-04T08:20:09-03:00,1,1,3,4,-2,1,2,feature/test-pipeline-2,4.25,1,marcusricardoaguiar,6
,🔖 Version bumped by fastlane. [skip ci],Other,2025-08-13T20:16:41+00:00,2025-08-13T15:54:22-06:00,1,29,33,62,-4,3,2,hotfix/1.18.1,12.55,2,Jenkins CI; José Miguel Rivera López,12
SP-2421,"🔀 support/bugfix/ONEAPP-SP-2421: Ensures transaction list updates when selecting any statement period, not just default.",Main,2025-07-11T15:29:06-06:00,2025-07-11T15:29:06-06:00,1,1,1,2,0,1,1,support/bugfix/ONEAPP-SP-2421,3.15,1,APPLAUDO,6
