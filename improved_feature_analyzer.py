#!/usr/bin/env python3
"""
Improved Feature Metrics Analysis Script for iOS Repository
Focuses on feature-specific commits only, not entire branch history
"""

import re
import csv
import subprocess
import json
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import os
import sys

class ImprovedFeatureAnalyzer:
    def __init__(self, repo_path="."):
        self.repo_path = repo_path
        self.jira_pattern = re.compile(r'(?i)([A-Z]{2,10}-\d+)')
        self.features = {}
        self.total_commits_analyzed = 0
        
    def run_git_command(self, command):
        """Execute git command and return output"""
        try:
            result = subprocess.run(
                command, shell=True, cwd=self.repo_path,
                capture_output=True, text=True, check=True
            )
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            print(f"Git command failed: {command}")
            print(f"Error: {e.stderr}")
            return ""
    
    def validate_repository_completeness(self):
        """Validate that we have complete repository history"""
        # Check if repository is shallow
        is_shallow = self.run_git_command("git rev-parse --is-shallow-repository")
        if is_shallow == "true":
            print("ERROR: Repository is shallow. Run 'git fetch --unshallow' first.")
            sys.exit(1)
        
        # Count total commits
        total_commits = self.run_git_command("git rev-list --all | wc -l")
        self.total_commits_analyzed = int(total_commits)
        print(f"✅ Repository validation: {self.total_commits_analyzed} total commits")
        
        return True
    
    def get_feature_specific_commits(self, branch_name, base_branch='develop'):
        """Get commits that are specific to the feature branch (not in base branch)"""
        try:
            # Get commits in feature branch but not in base branch
            feature_commits = self.run_git_command(
                f"git log --pretty=format:'%H|%ai|%an|%ae|%s' origin/{branch_name} --not origin/{base_branch}"
            )
            
            if not feature_commits:
                return []
            
            commits = []
            for line in feature_commits.split('\n'):
                if line.strip():
                    parts = line.split('|')
                    if len(parts) >= 5:
                        commits.append({
                            'hash': parts[0],
                            'date': parts[1],
                            'author': parts[2],
                            'email': parts[3],
                            'message': parts[4]
                        })
            
            return commits
            
        except Exception as e:
            print(f"Error getting feature commits for {branch_name}: {e}")
            return []
    
    def get_feature_statistics(self, branch_name, base_branch='develop'):
        """Get detailed statistics for feature-specific changes"""
        try:
            # Get numstat for feature-specific commits
            stats_output = self.run_git_command(
                f"git log --numstat --pretty=format:'' origin/{branch_name} --not origin/{base_branch}"
            )
            
            lines_added = 0
            lines_deleted = 0
            files_changed = set()
            
            for line in stats_output.split('\n'):
                if line.strip() and '\t' in line:
                    parts = line.split('\t')
                    if len(parts) >= 3:
                        try:
                            added = int(parts[0]) if parts[0] != '-' else 0
                            deleted = int(parts[1]) if parts[1] != '-' else 0
                            filename = parts[2]
                            
                            lines_added += added
                            lines_deleted += deleted
                            files_changed.add(filename)
                        except ValueError:
                            continue
            
            return {
                'lines_added': lines_added,
                'lines_deleted': lines_deleted,
                'files_changed': len(files_changed),
                'changed_files': list(files_changed)
            }
            
        except Exception as e:
            print(f"Error getting statistics for {branch_name}: {e}")
            return {
                'lines_added': 0,
                'lines_deleted': 0,
                'files_changed': 0,
                'changed_files': []
            }
    
    def analyze_feature_branch(self, branch_name):
        """Analyze a single feature branch for metrics"""
        print(f"  Analyzing: {branch_name}")
        
        # Extract Jira ticket from branch name
        jira_match = self.jira_pattern.search(branch_name)
        jira_ticket = jira_match.group(1).upper() if jira_match else None
        
        # Get feature-specific commits
        feature_commits = self.get_feature_specific_commits(branch_name)
        
        if not feature_commits:
            print(f"    No feature-specific commits found for {branch_name}")
            return None
        
        # Get feature statistics
        stats = self.get_feature_statistics(branch_name)
        
        # Calculate development timeline
        commit_dates = [datetime.fromisoformat(c['date'].replace('Z', '+00:00')) for c in feature_commits]
        commit_dates.sort()
        
        start_date = commit_dates[0]
        end_date = commit_dates[-1]
        total_dev_days = max((end_date - start_date).days, 1)
        
        # Get unique authors
        authors = list(set(c['author'] for c in feature_commits))
        
        # Identify primary module
        module = self.identify_primary_module(stats['changed_files'])
        
        # Calculate complexity score
        complexity_score = (
            stats['lines_added'] * 0.1 +
            stats['lines_deleted'] * 0.05 +
            stats['files_changed'] * 2 +
            len(feature_commits) * 1
        )
        
        # Get feature description from latest commit
        feature_description = feature_commits[0]['message'] if feature_commits else ''
        
        return {
            'jira_ticket': jira_ticket,
            'feature_description': feature_description,
            'module_component': module,
            'development_start_date': start_date.isoformat(),
            'development_end_date': end_date.isoformat(),
            'total_dev_days': total_dev_days,
            'lines_added': stats['lines_added'],
            'lines_deleted': stats['lines_deleted'],
            'lines_modified': stats['lines_added'] + stats['lines_deleted'],
            'total_line_count': stats['lines_added'] - stats['lines_deleted'],
            'files_changed': stats['files_changed'],
            'commits_count': len(feature_commits),
            'branch_name': branch_name,
            'complexity_score': round(complexity_score, 2),
            'team_size': len(authors),
            'developer_names': authors,
            'productivity_factor': round(6 * (len(authors) * total_dev_days), 2)
        }
    
    def identify_primary_module(self, changed_files):
        """Identify the primary module based on changed files"""
        module_counts = Counter()
        
        for file_path in changed_files:
            if 'Atlantida/Source/Modules/' in file_path:
                # Extract module name
                parts = file_path.split('Atlantida/Source/Modules/')
                if len(parts) > 1:
                    module = parts[1].split('/')[0]
                    module_counts[module] += 1
            elif 'Atlantida/Source/Components/' in file_path:
                module_counts['Components'] += 1
            elif 'Atlantida/Source/Core/' in file_path:
                module_counts['Core'] += 1
            elif 'Atlantida/Source/DesignSystem/' in file_path:
                module_counts['DesignSystem'] += 1
            elif '.swift' in file_path:
                module_counts['Swift Code'] += 1
            elif any(config in file_path for config in ['.yml', '.yaml', '.json', '.plist']):
                module_counts['Configuration'] += 1
            else:
                module_counts['Other'] += 1
        
        # Get the most affected module
        return module_counts.most_common(1)[0][0] if module_counts else 'Unknown'
    
    def get_all_feature_branches(self):
        """Get all feature, bugfix, and hotfix branches"""
        branches_output = self.run_git_command("git branch -r")
        branches = []
        
        for line in branches_output.split('\n'):
            branch = line.strip()
            if branch and not branch.startswith('origin/HEAD'):
                branch_name = branch.replace('origin/', '')
                if any(pattern in branch_name.lower() for pattern in ['feature/', 'bugfix/', 'hotfix/']):
                    branches.append(branch_name)
        
        return branches
    
    def analyze_all_features(self):
        """Main method to analyze all features in the repository"""
        print("🔍 Starting improved feature analysis...")
        
        # Validate repository
        self.validate_repository_completeness()
        
        # Get all feature branches
        feature_branches = self.get_all_feature_branches()
        print(f"📊 Found {len(feature_branches)} feature/bugfix/hotfix branches")
        
        # Process each feature branch
        processed_features = []
        
        for i, branch in enumerate(feature_branches, 1):
            print(f"Processing {i}/{len(feature_branches)}: {branch}")
            
            feature_info = self.analyze_feature_branch(branch)
            if feature_info:
                processed_features.append(feature_info)
        
        self.features = processed_features
        print(f"✅ Analysis complete: {len(processed_features)} features processed")
        
        return processed_features
    
    def export_to_csv(self, filename='improved_feature_metrics.csv'):
        """Export feature metrics to CSV"""
        if not self.features:
            print("No features to export")
            return
        
        fieldnames = [
            'jira_ticket', 'feature_description', 'module_component',
            'development_start_date', 'development_end_date', 'total_dev_days',
            'lines_added', 'lines_deleted', 'lines_modified', 'total_line_count',
            'files_changed', 'commits_count', 'branch_name', 'complexity_score',
            'team_size', 'developer_names', 'productivity_factor'
        ]
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for feature in self.features:
                # Convert developer_names list to string
                feature_copy = feature.copy()
                feature_copy['developer_names'] = '; '.join(feature.get('developer_names', []))
                
                # Ensure all required fields exist
                for field in fieldnames:
                    if field not in feature_copy:
                        feature_copy[field] = ''
                
                writer.writerow(feature_copy)
        
        print(f"✅ CSV exported: {filename} ({len(self.features)} features)")
    
    def generate_summary_report(self, filename='improved_analysis_summary.md'):
        """Generate summary analysis report"""
        if not self.features:
            print("No features to analyze")
            return
        
        # Calculate summary statistics
        total_features = len(self.features)
        dev_days = [f.get('total_dev_days', 0) for f in self.features if f.get('total_dev_days', 0) > 0]
        avg_dev_days = sum(dev_days) / len(dev_days) if dev_days else 0
        avg_complexity = sum(f.get('complexity_score', 0) for f in self.features) / total_features
        avg_team_size = sum(f.get('team_size', 0) for f in self.features) / total_features
        
        # Module distribution
        modules = Counter(f.get('module_component', 'Unknown') for f in self.features)
        
        # Developer contribution
        all_developers = []
        for f in self.features:
            all_developers.extend(f.get('developer_names', []))
        developer_counts = Counter(all_developers)
        
        # Feature size distribution
        small_features = len([f for f in self.features if f.get('total_dev_days', 0) <= 3])
        medium_features = len([f for f in self.features if 3 < f.get('total_dev_days', 0) <= 14])
        large_features = len([f for f in self.features if f.get('total_dev_days', 0) > 14])
        
        # Generate report
        report = f"""# Improved Repository Analysis Summary

## Overview
- **Total Features Analyzed**: {total_features}
- **Total Commits Analyzed**: {self.total_commits_analyzed}
- **Analysis Date**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Development Metrics
- **Average Development Time**: {avg_dev_days:.1f} days
- **Average Complexity Score**: {avg_complexity:.1f}
- **Average Team Size**: {avg_team_size:.1f} developers

## Feature Size Distribution
- **Small Features** (≤3 days): {small_features} ({(small_features/total_features)*100:.1f}%)
- **Medium Features** (4-14 days): {medium_features} ({(medium_features/total_features)*100:.1f}%)
- **Large Features** (>14 days): {large_features} ({(large_features/total_features)*100:.1f}%)

## Module Distribution
"""
        
        for module, count in modules.most_common():
            percentage = (count / total_features) * 100
            report += f"- **{module}**: {count} features ({percentage:.1f}%)\n"
        
        report += "\n## Top Contributors\n"
        
        for developer, count in developer_counts.most_common(10):
            report += f"- **{developer}**: {count} features\n"
        
        report += f"""
## Development Time Analysis
- **Median Development Time**: {sorted(dev_days)[len(dev_days)//2] if dev_days else 0:.1f} days
- **Fastest Feature**: {min(dev_days) if dev_days else 0} days
- **Longest Feature**: {max(dev_days) if dev_days else 0} days

## Repository Structure
- **Primary Language**: Swift (iOS)
- **Architecture**: Modular SwiftUI/UIKit
- **Main Modules**: {', '.join([m for m, _ in modules.most_common(5)])}
"""
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"✅ Summary report generated: {filename}")

def main():
    """Main execution function"""
    print("🚀 Starting Improved Feature Metrics Analysis")
    print("=" * 50)
    
    analyzer = ImprovedFeatureAnalyzer()
    
    # Run complete analysis
    features = analyzer.analyze_all_features()
    
    if not features:
        print("❌ No features found to analyze")
        return
    
    # Export results
    analyzer.export_to_csv()
    analyzer.generate_summary_report()
    
    print("=" * 50)
    print("✅ Analysis complete! Generated files:")
    print("  - improved_feature_metrics.csv")
    print("  - improved_analysis_summary.md")

if __name__ == "__main__":
    main()
