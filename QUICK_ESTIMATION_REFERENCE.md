# 🚀 Quick Feature Estimation Reference Card

*Based on analysis of 35 features from iOS Banking App*

## ⚡ Quick Estimates

### By Feature Type
| Feature Type | Typical Time | Examples |
|--------------|-------------|----------|
| **Bug Fix** | 1 day | UI fixes, text changes, minor logic |
| **Small Feature** | 1-3 days | New buttons, simple screens |
| **Medium Feature** | 4-14 days | API integration, workflow changes |
| **Large Feature** | 15+ days | Major refactoring, new modules |

### By Module (with multipliers)
| Module | Multiplier | Typical Time |
|--------|------------|-------------|
| **Components/DesignSystem** | 0.8x | Faster (isolated) |
| **Main** | 1.0x | Baseline |
| **TB-CreditCardMenu** | 1.2x | +20% (financial) |
| **Core** | 1.3x | +30% (infrastructure) |
| **AC-UserAuthentication** | 1.5x | +50% (security) |

## 📊 Statistical Baselines

- **Median**: 1 day (most common)
- **Average**: 7 days (including outliers)
- **57%** of features: ≤3 days
- **37%** of features: 4-14 days
- **6%** of features: >14 days

## 🎯 Estimation Formula

```
Estimated Days = Base Time × Module Multiplier × Risk Factor

Base Time:
- Simple: 1-3 days
- Standard: 4-8 days  
- Complex: 10-20 days

Risk Factors:
- Single module: 1.0x
- Cross-module: 1.2x
- New technology: 1.5x
- Security/Financial: 1.3x
- Team size >1: +20% per person
```

## ⚠️ Risk Indicators

### High Risk (+50% time)
- ❌ Cross-module changes
- ❌ Authentication/Security
- ❌ Financial operations
- ❌ New technology
- ❌ Team size >2

### Low Risk (use baseline)
- ✅ Single module
- ✅ UI-only changes
- ✅ Similar to recent work
- ✅ Single developer
- ✅ Clear requirements

## 🔢 Confidence Levels

| Confidence | Scenario | Buffer |
|------------|----------|--------|
| **High** | Similar features, experienced dev | ±20% |
| **Medium** | Standard features, some unknowns | ±40% |
| **Low** | New tech, complex integration | ±60% |

## 📋 Quick Checklist

### Before Estimating
- [ ] Identify primary module affected
- [ ] Count files likely to change
- [ ] Check for cross-module dependencies
- [ ] Assess team experience with similar features
- [ ] Consider testing and review time

### Red Flags
- [ ] Feature >30 days (scope creep risk)
- [ ] Multiple developers on simple feature
- [ ] Unclear requirements
- [ ] New technology without POC

## 💡 Pro Tips

1. **Start with median (1 day)** for simple changes
2. **Use average (7 days)** for standard features
3. **Add 30-50%** for testing and review
4. **Break large features** into smaller pieces
5. **Track actual vs. estimated** for learning

---
*Quick reference based on comprehensive analysis of 4,472 commits across 35 features*
