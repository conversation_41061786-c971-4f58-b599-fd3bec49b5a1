jira_ticket,feature_description,module_component,development_start_date,development_end_date,total_dev_days,lines_added,lines_deleted,lines_modified,total_line_count,files_changed,commits_count,branch_name,complexity_score,team_size,developer_names,productivity_factor
ONE-2627,💄ONE-2627 - Update padding spacing and scroll area.,Other,2022-02-16 13:11:20 -0600,2025-07-18 11:39:09 -0600,1247,886260,645917,1532177,240343,19665,3966,bugfix/ONE-2627-v2,164217.85,30,<PERSON>; <PERSON>; bryan <PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>; <PERSON>; <PERSON><PERSON><PERSON>; <PERSON>; <PERSON>; <PERSON>; <PERSON>; <PERSON><PERSON>; <PERSON>; <PERSON><PERSON>; <PERSON>; <PERSON>; <PERSON>; <PERSON>; <PERSON>; <PERSON><PERSON>; <PERSON>; <PERSON>; <PERSON>; jblanco-applau<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>; <PERSON>; j<PERSON><PERSON>.are<PERSON><PERSON>; <PERSON>,224460
ONE-2629,"🐛 ONE-2629 - Reformat validation, reset is handle on VM",Other,2022-02-16 13:11:20 -0600,2025-07-25 14:52:36 -06<PERSON>,1255,886432,64<PERSON>21,1532453,240411,19666,3981,<PERSON>-2629,164257.25,30,<PERSON>; <PERSON> <PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON> <PERSON>; <PERSON>; <PERSON>; <PERSON>; <PERSON>; <PERSON>; <PERSON>; <PERSON> <PERSON>; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,225900
ONE-3189,Merge branch 'develop' into feature/ONE-3189,Other,2022-02-16 13:11:20 -0600,2025-10-06 15:50:40 -0600,1328,905979,655251,1561230,250728,20301,4103,feature/ONE-3189,168065.45,34,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Marcus Aguiar; Josseh Blanco; remejia-applaudo; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; jlorenzanaBancatlan; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Emilio Vásquez; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; jrico-applaudo; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,270912
,🔖 Version bumped by fastlane. [skip ci],Other,2022-02-16 13:11:20 -0600,2025-08-13 15:54:22 -0600,1274,886640,646080,1532720,240560,19668,3987,hotfix/1.18.1,164291.0,30,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Marcus Aguiar; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,229320
,🎨 ONE-AddGithubReviewers - Improve pull request template,Other,2022-02-16 13:11:20 -0600,2025-09-25 17:02:43 -0600,1317,903147,651697,1554844,251450,20072,4078,feature/ONE-AddGithubReviewers,167121.55,33,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Marcus Aguiar; Josseh Blanco; remejia-applaudo; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Emilio Vásquez; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; jrico-applaudo; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,260766
ONE-2324,Merged develop into bugfix/ONE-2324,Other,2022-02-16 13:11:20 -0600,2025-09-19 17:36:39 +0000,1310,901563,651452,1553015,250111,20029,4069,bugfix/ONE-2324,166855.9,30,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Marcus Aguiar; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,235800
ONE-3020,Merged develop into feature/ONE-3020,Other,2022-02-16 13:11:20 -0600,2025-09-23 23:55:32 +0000,1315,902872,652039,1554911,250833,20069,4088,feature/ONE-3020,167115.15,30,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Marcus Aguiar; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,236700
ONE-2821,✏️ ONE-2821 - Fix typo,Other,2022-02-16 13:11:20 -0600,2025-08-12 16:39:06 -0600,1273,893803,649255,1543058,244548,19806,4017,feature/ONE-2821,165472.05,30,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Marcus Aguiar; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,229140
ONE-3058,💚 ONE-3058 - Upload XML report dependency check,Other,2022-02-16 13:11:20 -0600,2025-09-23 14:21:20 -0600,1315,901753,651570,1553323,250183,20050,4070,feature/ONE-3058-v2,166923.8,30,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Marcus Aguiar; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,236700
ONE-3138,🐛 ONE-3138 - add space after filter,Other,2022-02-16 13:11:20 -0600,2025-09-17 13:23:11 -0600,1309,901182,651339,1552521,249843,20026,4066,bugfix/ONE-3138,166803.15,30,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Marcus Aguiar; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,235620
ONE-2259,♻️ ONE-2259 - Include CustomPageIndicatorStyleConfiguration.,Other,2022-02-16 13:11:20 -0600,2025-09-30 15:47:43 -0600,1322,903591,651512,1555103,252079,20017,4096,feature/ONE-2259-Part2,167064.7,30,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Marcus Aguiar; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,237960
ONE-2348,✅ ONE-2348 - Fix broken unit tests,Other,2022-02-16 13:11:20 -0600,2025-06-09 23:08:51 -0600,1209,881021,643377,1524398,237644,19530,3946,feature/ONE-2348,163276.95,29,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,210366
ONE-2768,🚧 ONE-2768 - Add last files,Other,2022-02-16 13:11:20 -0600,2025-08-07 17:39:19 -0600,1268,890152,648642,1538794,241510,19754,3984,feature/ONE-2768,164939.3,30,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Marcus Aguiar; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,228240
ONE-3231,🔀 ONE-3259 - Resize close button and prevent scroll in Overdue Payments Sheet (#18),Other,2022-02-16 13:11:20 -0600,2025-10-06 14:41:44 -0600,1328,904755,654831,1559586,249924,20211,4085,feature/ONE-3231,167724.05,34,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Marcus Aguiar; Josseh Blanco; remejia-applaudo; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; jlorenzanaBancatlan; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Emilio Vásquez; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; jrico-applaudo; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,270912
ONE-1417,🚧 ONE-1417 - Combine service calls,Other,2022-02-16 13:11:20 -0600,2025-06-10 08:59:01 -0600,1209,877423,640662,1518085,236761,19365,3896,bugfix/ONE-1417,162401.4,29,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,210366
ONE-3138,🐛 ONE-3138v1 - replace published variable to a computed variable with filters and validations.,Other,2022-02-16 13:11:20 -0600,2025-09-22 10:45:39 -0600,1313,899598,650835,1550433,248763,19973,4061,bugfix/ONE-3138v1,166508.55,30,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Marcus Aguiar; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,236340
ONE-2901,🚧 ONE-2901 - Include row builder.,Other,2022-02-16 13:11:20 -0600,2025-09-23 15:27:57 -0600,1315,901944,651559,1553503,250385,20044,4070,feature/ONE-2901,166930.35,30,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Marcus Aguiar; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,236700
,🔖 Version bumped by fastlane. [skip ci],Other,2022-02-16 13:11:20 -0600,2025-05-21 18:52:11 -0600,1190,774519,539192,1313711,235327,18618,3785,hotfix/1.16.1,145432.5,27,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Josseh Blanco; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,192780
ONE-2627,🔥 ONE-2627 - Update padding spacing and scroll area.,Other,2022-02-16 13:11:20 -0600,2025-07-15 14:58:45 -0600,1245,886243,645905,1532148,240338,19665,3965,bugfix/ONE-2627,164214.55,30,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Marcus Aguiar; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,224100
,🔖 Version bumped by fastlane. [skip ci],Other,2022-02-16 13:11:20 -0600,2025-07-17 18:23:59 -0600,1247,842509,601717,1444226,240792,18729,3820,hotfix/1.17.1,155614.75,27,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Josseh Blanco; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,202014
SP-2421,"🔀 support/bugfix/ONEAPP-SP-2421: Ensures transaction list updates when selecting any statement period, not just default.",Other,2022-02-16 13:11:20 -0600,2025-07-11 15:29:06 -0600,1241,842431,601667,1444098,240764,18729,3818,support/bugfix/ONEAPP-SP-2421,155602.45,28,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Josseh Blanco; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; APPLAUDO; jaime.arevalo; Vladimir Guevara,208488
ONE-2376,♻️ ONE-2376 - Refactor Login Response Data,Other,2022-02-16 13:11:20 -0600,2025-06-04 16:15:26 -0600,1204,875800,639493,1515293,236307,19294,3892,feature/ONE-2376,162034.65,30,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Miguel Rivera; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,216720
ONE-3018,🔀 Merge branch 'develop' into feature/ONE-3018,Other,2022-02-16 13:11:20 -0600,2025-09-22 14:09:35 -0600,1314,901734,651601,1553335,250133,20052,4074,feature/ONE-3018,166931.45,31,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Miguel Rivera; Marcus Aguiar; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,244404
ONE-3058,🔀 ONE-3031 - Update the selfie scan reason copies (pull request #1147),Other,2022-02-16 13:11:20 -0600,2025-09-19 00:05:08 +0000,1310,901548,651443,1552991,250105,20027,4064,feature/ONE-3058,166844.95,30,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Marcus Aguiar; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,235800
ONE-2579,🎨 ONE-2579 - Remove extra line,Other,2022-02-16 13:11:20 -0600,2025-07-23 15:30:30 -0600,1253,889736,647346,1537082,242390,19719,4000,feature/ONE-2579,164778.9,30,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Marcus Aguiar; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,225540
ONE-2627,💄ONE-2627 - Move padding to computed property `header` and `invitedMessage`.,Other,2022-02-16 13:11:20 -0600,2025-07-17 11:25:27 -0600,1246,886273,645931,1532204,240342,19665,3969,bugfix/ONE-2627-update,164222.85,30,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Marcus Aguiar; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,224280
ONE-2797,🐛 ONE-2797 - Fix card delivery summary UI,Other,2022-02-16 13:11:20 -0600,2025-07-25 11:21:01 -0600,1254,890097,648625,1538722,241472,19748,3980,bugfix/ONE-2797,164916.95,30,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Marcus Aguiar; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,225720
ONE-2046,♻️ ONE-2046 - Refactor and reorganize component `HomeView` and `HomeCardDetailView` to apply shimmering effect,Other,2022-02-16 13:11:20 -0600,2025-04-28 16:06:27 -0600,1167,779617,541115,1320732,238502,18725,3785,feature/ONE-2046,146252.45,27,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Josseh Blanco; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; Emely Melgar; David Cortes; Alexander Sosa; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,189054
ONE-2522,🎨 ONE-2522 - Add final network connection logic proposal,Other,2022-02-16 13:11:20 -0600,2025-07-14 15:41:56 -0600,1244,887041,646073,1533114,240968,19670,3967,feature/ONE-2522,164314.75,30,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Marcus Aguiar; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,223920
PIPELINE-2,PR to test the pipeline trigger,Other,2022-02-16 13:11:20 -0600,2025-07-04 08:20:09 -0300,1233,884546,644925,1529471,239621,19588,3950,feature/test-pipeline-2,163826.85,31,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; marcusricardoaguiar; Julio Rico; William Alfaro; Marcus Aguiar; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,229338
,🔖 Version bumped by fastlane. [skip ci],Other,2022-02-16 13:11:20 -0600,2025-08-15 00:19:15 -0600,1275,886637,646073,1532710,240564,19668,3986,hotfix/1.18.1v,164289.35,30,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Marcus Aguiar; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,229500
ONE-3170,🌐 ONE-3170 - Add some missing default errors,Other,2022-02-16 13:11:20 -0600,2025-10-03 22:07:02 -0600,1325,904167,651773,1555940,252394,20091,4085,feature/ONE-3170,167272.35,33,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Marcus Aguiar; Josseh Blanco; remejia-applaudo; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Emilio Vásquez; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; jrico-applaudo; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,262350
,🔀 Sync Merge branch 'develop' into feature/ONE-ServicesPayment,Other,2022-02-16 13:11:20 -0600,2025-05-28 12:19:29 -0600,1196,875580,639276,1514856,236304,19274,3884,feature/ONE-ServicesPayment,161953.8,29,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,208104
ONE-2378,♻️ ONE-2378 - Refactor Login Response Data,Other,2022-02-16 13:11:20 -0600,2025-06-04 16:55:11 -0600,1204,875843,639534,1515377,236309,19294,3891,feature/ONE-2378,162040.0,30,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Miguel Rivera; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,216720
ONE-2797,🐛 ONE-2797 - Fix missing UI update on additional card/third attempt flows,Other,2022-02-16 13:11:20 -0600,2025-07-29 17:32:30 -0600,1259,890869,648671,1539540,242198,19760,3984,bugfix/ONE-2797-v3,165024.45,30,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Marcus Aguiar; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,226620
ONE-2222,💄 ONE-2222 - Fix visual issues,Other,2022-02-16 13:11:20 -0600,2025-06-10 14:46:59 -0600,1210,878764,641820,1520584,236944,19463,3915,bugfix/ONE-2222,162808.4,29,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,210540
,🔖 Version bumped by fastlane. [skip ci],Other,2022-02-16 13:11:20 -0600,2025-09-01 15:29:58 -0600,1293,888741,646479,1535220,242262,19778,3997,hotfix/1.18.2,164751.05,30,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Marcus Aguiar; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,232740
ONE-3188,♻️ONE-3188 - Rename `OneCardPinCheckpointHandler` to `OneCardPinCheckpointInteractor`.,Other,2022-02-16 13:11:20 -0600,2025-10-06 16:59:07 -0600,1328,904708,653218,1557926,251490,20166,4105,feature/ONE-3188,167568.7,34,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Marcus Aguiar; Josseh Blanco; remejia-applaudo; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; jlorenzanaBancatlan; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Emilio Vásquez; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; jrico-applaudo; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,270912
ONE-3158,Merge remote-tracking branch 'origin/develop' into feature/ONE-3158,Other,2022-02-16 13:11:20 -0600,2025-10-06 15:04:30 -0600,1328,905366,654834,1560200,250532,20234,4088,feature/ONE-3158,167834.3,35,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Miguel Rivera; Marcus Aguiar; Josseh Blanco; remejia-applaudo; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; jlorenzanaBancatlan; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Emilio Vásquez; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; jrico-applaudo; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,278880
ONE-3169,🌐 ONE-3169 - Create internationalization for CreditCardStyle,Other,2022-02-16 13:11:20 -0600,2025-09-30 10:24:46 -0600,1321,902476,651630,1554106,250846,20062,4078,feature/ONE-3169,167031.1,30,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Marcus Aguiar; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,237780
ONE-3259,💄 ONE-3259 - Resize close button and prevent scroll,Other,2022-02-16 13:11:20 -0600,2025-10-06 13:06:18 -0600,1327,904755,654831,1559586,249924,20211,4085,bugfix/ONE-3259,167724.05,34,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Marcus Aguiar; Josseh Blanco; remejia-applaudo; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; jlorenzanaBancatlan; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Emilio Vásquez; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; jrico-applaudo; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,270708
ONE-2377,♻️ ONE-2377 - Unify the Profile and GetLastLogin Endpoint,Other,2022-02-16 13:11:20 -0600,2025-06-04 11:00:47 -0600,1203,875721,639337,1515058,236384,19276,3888,feature/ONE-2377,161978.95,30,Mario Mejía; Mario Enrique Mejia Vasquez; bryan garcia; Lyckan; Julio Rico; William Alfaro; Miguel Rivera; Josseh Blanco; Javier Lorenzana; Jaime Arévalo; José De la O; Emilio Vasquez; Mauricio Jovel; José Miguel Rivera López; Emely Melgar; Alexander Sosa; David Cortes; Jaime Mejia; Bryan Alexis García García; Edgar Emilio Vásquez Castillo; Cesar Callejas; Mario Mejía; Jenkins CI; César Callejas; jblanco-applaudostudios; Benjamin Rivera; Rodrigo Mejia; Gabriel Campos; jaime.arevalo; Vladimir Guevara,216540
ONEAPP-808,🔀 ONEAPP-808 - Fix navigation error in AFP flow (pull request #541),Unknown,2024-07-02 23:16:01 +0000,2024-07-02 23:16:01 +0000,1,17,12,29,5,3,1,orphaned/ONEAPP-808,9.3,1,Rodrigo Mejia,6
ONEAPP-937,🔀 ONEAPP-937 - Fixed issue on phone numbers mask (pull request #527),Unknown,2024-06-28 19:38:53 +0000,2024-06-28 19:38:53 +0000,1,1,1,2,0,1,1,orphaned/ONEAPP-937,3.15,1,David Cortes,6
ONEAPP-1267,🔀 ONEAPP-1267 - Update CredoLab dependencies using CocoaPods and upgrade to the latest version. (pull request #624),Unknown,2024-08-16 21:41:30 +0000,2024-08-16 21:41:30 +0000,1,48,12331,12379,-12283,159,1,orphaned/ONEAPP-1267,940.35,1,Emely Melgar,6
ONE-1466,🔀ONE-1466 - Remove unused image assets from Xcode project  (pull request #793),Unknown,2025-01-17 17:15:17 +0000,2025-01-17 17:15:17 +0000,1,21,3068,3089,-3047,345,1,orphaned/ONE-1466,846.5,1,Rodrigo Mejia,6
TLNTD-1283,🔀 TLNTD-1283 - Verify my AddressDUI Improvement (pull request #219),Unknown,2023-12-14 15:22:41 +0000,2023-12-14 15:22:41 +0000,1,160,74,234,86,14,1,orphaned/TLNTD-1283,48.7,1,Edgar Emilio Vásquez Castillo,6
TLNTD-1193,🔀 TLNTD-1193 - Fix issues with legacy apicall token refresh (pull request #185),Unknown,2023-11-15 00:52:27 +0000,2023-11-15 00:52:27 +0000,1,597,740,1337,-143,14,1,orphaned/TLNTD-1193,125.7,1,Josseh Blanco,6
ONE-1693,🔀 ONE-1693 - Optimization in Menu of the application (pull request #914),Unknown,2025-05-27 15:04:20 +0000,2025-05-27 15:04:20 +0000,1,700,154,854,546,17,1,orphaned/ONE-1693,112.7,1,José Miguel Rivera López,6
ONE-1555,🔀 ONE-1555 - Update Onboarding screens to match Figma design (pull request #826),Unknown,2025-03-19 18:11:19 +0000,2025-03-19 18:11:19 +0000,1,165,46,211,119,6,1,orphaned/ONE-1555,31.8,1,Julio Rico,6
TLNTD-335,🔀 TLNTD-335 - Add security headers and refresh token logic (pull request #123),Unknown,2023-10-13 15:12:16 +0000,2023-10-13 15:12:16 +0000,1,7494,2604,10098,4890,391,1,orphaned/TLNTD-335,1662.6,1,Josseh Blanco,6
TLNTD-1225,🔀 TLNTD-1225 - Changes memberId from int to String. (pull request #203),Unknown,2023-11-20 17:17:42 +0000,2023-11-20 17:17:42 +0000,1,110,131,241,-21,9,1,orphaned/TLNTD-1225,36.55,1,Rodrigo Mejia,6
ONE-2995,🔀 ONE-2995 - Fix the try quality and try face alerts' bullet format in the identity validation checkpoint (pull request #1138),Unknown,2025-09-11 15:33:28 +0000,2025-09-11 15:33:28 +0000,1,2,2,4,0,1,1,orphaned/ONE-2995,3.3,1,Josseh Blanco,6
ONE-1299,🔀 ONE-1288 - Include the Dynatraces custom actions. (pull request #728),Unknown,2024-11-27 16:55:50 +0000,2024-11-28 23:52:48 +0000,1,1730,405,2135,1325,121,2,orphaned/ONE-1299,437.25,1,Emely Melgar,6
ONEAPP-248,🔀 ONEAPP-248 - fetch card information in CardActivationDigitsInteracto,Unknown,2024-04-16 18:08:21 +0000,2024-04-16 18:08:21 +0000,1,110,13,123,97,6,1,orphaned/ONEAPP-248,24.65,1,Vladimir Guevara,6
ONE-3156,🔀 ONE-3156 - Update the document scan tutorial design  (#9),Unknown,2025-09-29 14:10:08 -0600,2025-09-29 14:10:08 -0600,1,200,225,425,-25,19,1,orphaned/ONE-3156,70.25,1,Josseh Blanco,6
ONEAPP-880,🔀 ONEAPP-880 - Change the card customization name limit to 21 characters. (pull request #523),Unknown,2024-06-27 17:10:01 +0000,2024-06-27 17:10:01 +0000,1,4,4,8,0,3,1,orphaned/ONEAPP-880,7.6,1,Josseh Blanco,6
TLNTD-473,🔀 TLNTD-473 - Fix UI issues in the DUI and guest view. (pull request #18),Unknown,2023-07-15 00:38:59 +0000,2023-07-15 00:38:59 +0000,1,11,5,16,6,5,1,orphaned/TLNTD-473,12.35,1,Emely Melgar,6
TLNTD-1238,🔀 TLNTD-1238- Fix reposition flow for additional cards (pull request #213),Unknown,2023-12-07 22:44:58 +0000,2023-12-07 22:44:58 +0000,1,141,42,183,99,11,1,orphaned/TLNTD-1238,39.2,1,Rodrigo Mejia,6
ONE-1139,🔀 ONE-1139 - Set correct rejection screens on biometry step (OB) (pull request #997),Unknown,2025-06-18 19:32:33 +0000,2025-06-18 19:32:33 +0000,1,11,94,105,-83,4,1,orphaned/ONE-1139,14.8,1,Rodrigo Mejia,6
ONEAPP-240,🔀 ONEAPP-240 - Unrecognized transactions (pull request #552),Unknown,2024-07-19 21:46:18 +0000,2024-07-19 21:46:18 +0000,1,2263,66,2329,2197,57,1,orphaned/ONEAPP-240,344.6,1,Edgar Emilio Vásquez Castillo,6
ONE-2581,🔀 ONE-2581 - Fix Protection plan screen spacing. (pull request #1040),Unknown,2025-07-11 15:51:42 +0000,2025-07-11 15:51:42 +0000,1,17,13,30,4,1,1,orphaned/ONE-2581,5.35,1,Julio Rico,6
