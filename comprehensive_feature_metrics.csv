jira_ticket,feature_description,module_component,development_start_date,development_end_date,total_dev_days,lines_added,lines_deleted,lines_modified,total_line_count,files_changed,commits_count,branch_name,complexity_score,team_size,developer_names,productivity_factor
ONE-3188,♻️ONE-3188 - Rename `OneCardPinCheckpointHandler` to `OneCardPinCheckpointInteractor`.,Main,2025-10-01T14:29:02-06:00,2025-10-06T16:59:07-06:00,5,584,221,805,363,40,24,direct_commit,173.45,1,<PERSON>,30
ONE-3189,Merge branch 'develop' into feature/ONE-3189,Swift Code,2025-09-30T09:47:13-06:00,2025-10-06T15:50:40-06:00,6,2424,3121,5545,-697,251,18,direct_commit,918.45,1,<PERSON><PERSON><PERSON>,36
ONE-3158,Merge remote-tracking branch 'origin/develop' into feature/ONE-3158,Swift Code,2025-10-01T08:27:23-06:00,2025-10-06T15:04:30-06:00,5,2012,2930,4942,-918,195,3,direct_commit,740.7,1,<PERSON>,30
ONE-3259,🔀 ONE-3259 - Resize close button and prevent scroll in Overdue Payments Sheet (#18),Main,2025-10-06T13:06:18-06:00,2025-10-06T14:41:44-06:00,1,4,4,8,0,1,2,direct_commit,4.6,2,remejia-applaudo; Rodrigo Mejia,12
ONE-3102,🔀 ONE-3102 - Update email label for the automatic services payment detail. (#17),Core,2025-10-06T12:05:50-06:00,2025-10-06T12:05:50-06:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Emely Melgar,6
ONE-3170,🌐 ONE-3170 - Add some missing default errors,Components,2025-09-25T14:41:31-06:00,2025-10-03T22:07:02-06:00,8,1552,184,1736,1368,55,11,direct_commit,285.4,1,Emilio Vasquez,48
ONE-2991,🔀 ONE-2991 - Modify text to match with Design on Figma (#16),TB-Menu,2025-10-03T09:40:42-06:00,2025-10-03T09:40:42-06:00,1,2,2,4,0,1,1,direct_commit,3.3,1,jlorenzanaBancatlan,6
ONE-2771,🔀 ONE-2771 - Remove commented files and move remaining legacy files (#15),Core,2025-10-03T09:19:45-06:00,2025-10-03T09:19:45-06:00,1,17,529,546,-512,20,1,direct_commit,69.15,1,remejia-applaudo,6
ONE-3227,🔀 ONE-3227 - Remove and relocate legacy files (Pt. 2) (#14),Swift Code,2025-10-02T09:04:42-06:00,2025-10-02T09:04:42-06:00,1,610,1301,1911,-691,99,1,direct_commit,325.05,1,remejia-applaudo,6
ONE-3224,🔀 ONE-3224 - Update MainCarousel behaviour and include a CustomPageIndicator. (#12),Components,2025-10-01T14:22:28-06:00,2025-10-01T14:22:28-06:00,1,537,122,659,415,12,1,direct_commit,84.8,1,Emely Melgar,6
ONE-3226,🔀 ONE-3226 - Remove unused files and mark used legacy files (#11),Swift Code,2025-10-01T11:28:38-06:00,2025-10-01T11:28:38-06:00,1,188,914,1102,-726,67,1,direct_commit,199.5,1,remejia-applaudo,6
ONE-3169,🔀 ONE-3169 - Apply new design on select names section (#6),TB-Home,2025-09-24T20:29:58-06:00,2025-09-30T17:58:35-06:00,5,774,98,872,676,44,9,direct_commit,179.3,2,Javier Lorenzana; jlorenzanaBancatlan,60
ONE-2259,♻️ ONE-2259 - Include CustomPageIndicatorStyleConfiguration.,Main,2025-06-05T15:22:22+00:00,2025-09-30T15:47:43-06:00,117,24799,8863,33662,15936,1045,41,direct_commit,5054.05,1,Emely Melgar,702
ONE-1652,🔀 ONE-1652 - Revert usage of `logApplicationNameApp` for `description` in log section. (#10),TB-Menu,2025-09-26T09:43:57-06:00,2025-09-29T14:45:03-06:00,3,2,2,4,0,1,2,direct_commit,4.3,1,jrico-applaudo,18
ONE-3156,🔀 ONE-3156 - Update the document scan tutorial design  (#9),DesignSystem,2025-09-29T14:10:08-06:00,2025-09-29T14:10:08-06:00,1,200,225,425,-25,19,1,direct_commit,70.25,1,Josseh Blanco,6
ONE-3036,🔀  ONE-3036 - Add new popToFirst function and move the users to the document scan view when identification image fails (#1),Navigation,2025-09-25T10:50:17-06:00,2025-09-25T10:50:17-06:00,1,145,20,165,125,7,1,direct_commit,30.5,1,Josseh Blanco,6
ONE-1513,🔀 ONE-1513 - Fix alert message for main cancellation card. (#4),Swift Code,2025-09-25T10:44:09-06:00,2025-09-25T10:44:09-06:00,1,350,73,423,277,13,1,direct_commit,65.65,1,jrico-applaudo,6
ONE-3194,🔀 ONE-3194 -  Update path and response with new MenuAlerts endpoint spec (#3),Core,2025-09-24T15:53:00-06:00,2025-09-24T15:53:00-06:00,1,33,1,34,32,2,1,direct_commit,8.35,1,remejia-applaudo,6
ONE-3020,🔀 ONE-3020 - Add Overdue Payments account Status empty states and alert (pull request #1150),Main,2025-09-17T16:10:18-06:00,2025-09-24T17:07:29+00:00,6,3316,738,4054,2578,120,20,direct_commit,628.5,1,Rodrigo Mejia,36
ONE-3167,🔀 ONE-3167 - Fix parsing of Purchase Amount property in ticket detail domain (pull request #1154),Swift Code,2025-09-24T16:32:36+00:00,2025-09-24T16:32:36+00:00,1,7,7,14,0,3,1,direct_commit,8.05,1,Rodrigo Mejia,6
ONE-2901,🚧 ONE-2901 - Include row builder.,Main,2025-09-22T12:22:32-06:00,2025-09-23T15:27:57-06:00,1,290,30,320,260,8,3,direct_commit,49.5,1,Emely Melgar,6
ONE-3058,💚 ONE-3058 - Upload XML report dependency check,Other,2025-09-23T17:29:38+00:00,2025-09-23T14:21:20-06:00,1,51,1,52,50,4,2,direct_commit,15.15,2,Edgar Emilio Vásquez Castillo; Emilio Vasquez,12
ONE-3018,🔀 ONE-3018 - Update new logo icon (pull request #1139),DesignSystem,2025-09-12T08:31:49-06:00,2025-09-23T16:55:43+00:00,11,2211,833,3044,1378,144,8,direct_commit,558.75,3,Javier Lorenzana; José Miguel Rivera López; Miguel Rivera,198
ONE-3138,🔀 ONE-3138 - Fix double divider and empty space on card menu. (pull request #1146),TB-CreditCardMenu,2025-09-17T11:21:22-06:00,2025-09-23T16:53:42+00:00,5,42,45,87,-3,2,12,direct_commit,22.45,1,Javier Lorenzana,30
ONE-3164,🔀 ONE-3164 - Group ticket detail files. (pull request #1151),{TB-Menu,2025-09-19T23:00:12+00:00,2025-09-19T23:00:12+00:00,1,92,78,170,14,11,1,direct_commit,36.1,1,Emely Melgar,6
ONE-2324,🔀 ONE-2324 - Fixes on copy text in first login section. (pull request #1149),Main,2025-09-18T15:35:06-06:00,2025-09-19T22:34:44+00:00,1,331,98,429,233,11,5,direct_commit,65.0,1,Julio Rico,6
ONE-2613,🔀 ONE-2613 - Add Progress bar on Demographics (pull request #1148),OB-Onboarding,2025-09-19T17:29:45+00:00,2025-09-19T17:29:45+00:00,1,6,0,6,6,1,1,direct_commit,3.6,1,Javier Lorenzana,6
ONE-3031,🔀 ONE-3031 - Update the selfie scan reason copies (pull request #1147),Main,2025-09-19T00:05:08+00:00,2025-09-19T00:05:08+00:00,1,308,81,389,227,7,1,direct_commit,49.85,1,Josseh Blanco,6
ONE-3016,🔀 ONE-3016 - Keep tab bar visible in Main Menu and Credit Card Menu (pull request #1140) (pull request #1140),TB-Menu,2025-09-18T16:16:16+00:00,2025-09-18T16:16:16+00:00,1,64,28,92,36,6,1,direct_commit,20.8,1,Julio Rico,6
ONE-3017,"🔀 ONE-3017 - Include Dynatrace events for CreditCardMenu, CardOptions and CardPin features. (pull request #1141)",Swift Code,2025-09-16T20:39:41+00:00,2025-09-16T20:39:41+00:00,1,947,10,957,937,16,1,direct_commit,128.2,1,Emely Melgar,6
ONE-3030,🔀 ONE-3030 - Modify delivery disclaimer text (pull request #1143),Main,2025-09-16T20:39:02+00:00,2025-09-16T20:39:02+00:00,1,3,3,6,0,3,1,direct_commit,7.45,1,Javier Lorenzana,6
ONE-3125,🐛[ONE-3125] - Prevent MenuAlerts failure to stop login (pull request #1142),AC-UserAuthentication,2025-09-16T16:40:02+00:00,2025-09-16T16:40:02+00:00,1,27,12,39,15,2,1,direct_commit,8.3,1,Rodrigo Mejia,6
ONE-1761,🔀 ONE-1761 - Fix text copies in Unrecognized Transactions section for Credit card. (pull request #1137),Main,2025-09-11T18:20:19+00:00,2025-09-11T18:20:19+00:00,1,5,2,7,3,3,1,direct_commit,7.6,1,Julio Rico,6
ONE-2899,🔀 ONE-2899 - Add improvements for SonarQube report (pull request #1135),Other,2025-09-11T17:49:06+00:00,2025-09-11T17:49:06+00:00,1,24,12,36,12,3,1,direct_commit,10.0,1,Edgar Emilio Vásquez Castillo,6
ONE-2995,🔀 ONE-2995 - Fix the try quality and try face alerts' bullet format in the identity validation checkpoint (pull request #1138),OB-Onboarding,2025-09-11T15:33:28+00:00,2025-09-11T15:33:28+00:00,1,2,2,4,0,1,1,direct_commit,3.3,1,Josseh Blanco,6
ONE-2860,🔀 ONE-2860 -  Use new MenuAlertsV2 and refactor notification presentation logic (pull request #1134),Main,2025-09-10T23:38:03+00:00,2025-09-10T23:38:03+00:00,1,596,373,969,223,28,1,direct_commit,135.25,1,Rodrigo Mejia,6
ONE-2978,🔀 ONE-2978 Weight Reduction - Optimize Assets (Create Subsets for Fonts) (pull request #1136),DesignSystem,2025-09-10T21:59:19+00:00,2025-09-10T21:59:19+00:00,1,0,111,111,-111,39,1,direct_commit,84.55,1,José Miguel Rivera López,6
ONE-3025,🔀 ONE-3025 - Move to guest view temporarily. (pull request #1133),OB-Onboarding,2025-09-05T15:08:23+00:00,2025-09-05T15:08:23+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Emely Melgar,6
ONE-2315,"🔀 ONE-2315 - Update text copies in Surveys, Contracts and Delivery options (pull request #1129)",Main,2025-09-04T15:37:03+00:00,2025-09-04T15:37:03+00:00,1,298,40,338,258,9,1,direct_commit,50.8,1,Julio Rico,6
ONE-3019,🔀 ONE-3019 - Fix handler error replacing Embeddable for OptionalEmbeddable.(pull request #1131),Core,2025-09-04T00:02:35+00:00,2025-09-04T00:02:35+00:00,1,2,2,4,0,2,1,direct_commit,5.3,1,Emely Melgar,6
MERGE-1,🔀 Merged in sync/merge-1.18.2-into-develop (pull request #1130),DesignSystem,2024-09-18T12:31:45-06:00,2025-09-03T21:50:19+00:00,350,11917,5085,17002,6832,332,14,direct_commit,2123.95,1,Emely Melgar,2100
ONE-2559,🚧 ONE-2559 - Update ResetPasswordForm logic.,AC-UserAuthentication,2025-09-03T10:27:37-06:00,2025-09-03T10:27:37-06:00,1,173,180,353,-7,4,1,direct_commit,35.3,1,Emely Melgar,6
ONE-2770,🔀 ONE-2770 Weight Reduction - Optimize assets (Change R.swift Localized Strings to native ones) (pull request #1106),Components,2025-09-03T15:56:15+00:00,2025-09-03T15:56:15+00:00,1,2127,328,2455,1799,75,1,direct_commit,380.1,1,José Miguel Rivera López,6
ONE-2529, ♻️ ONE-2529 - Update repository to support process type.,AC-UserAuthentication,2025-09-03T09:08:02-06:00,2025-09-03T09:08:02-06:00,1,45,19,64,26,3,1,direct_commit,12.45,1,Emely Melgar,6
ONE-2993,🔀 ONE-2993 - Refactor selfie template to handle different and future reason.(pull request #1127),Main,2025-09-02T16:39:04+00:00,2025-09-02T16:39:04+00:00,1,207,83,290,124,9,1,direct_commit,43.85,1,Emely Melgar,6
ONE-2919,🔀 ONE-2919 - Fix service /validate-forbidden-word error handling (pull request #1112),Components,2025-09-01T20:41:49+00:00,2025-09-01T20:41:49+00:00,1,30,5,35,25,2,1,direct_commit,8.25,1,José Miguel Rivera López,6
ONE-2944,🔀 ONE-2944 - Show biometry activation sheet after successful password update (pull request #1126),Components,2025-09-01T17:36:33+00:00,2025-09-01T17:36:33+00:00,1,14,5,19,9,1,1,direct_commit,4.65,1,Julio Rico,6
ONE-2980,🔀 ONE-2980 - Move the IdentificationImages endpoint after ccapplication request. (pull request #1125),Core,2025-08-29T22:43:48+00:00,2025-08-29T22:43:48+00:00,1,401,341,742,60,14,1,direct_commit,86.15,1,Emely Melgar,6
ONE-259,🚧 ONE-259 - Include the new user validation to recovery user and password.,Core,2025-08-29T08:54:39-06:00,2025-08-29T08:54:39-06:00,1,24,1,25,23,3,1,direct_commit,9.45,1,Emely Melgar,6
ONE-2286,🔀 ONE-2286 - Improve on deleting on OTP verification code. (pull request #1118),AC-UserAuthentication,2025-08-29T14:00:37+00:00,2025-08-29T14:00:37+00:00,1,181,11,192,170,8,1,direct_commit,35.65,1,Julio Rico,6
ONE-2987,🔀 ONE-2987 - Remove unnecessary X button from the biometry blocked alert (pull request #1124),OB-Onboarding,2025-08-28T22:15:48+00:00,2025-08-28T22:15:48+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Josseh Blanco,6
ONE-2986,🔀 ONE-2986 - Remove unnecessary flag checker (pull request #1123),TB-Menu,2025-08-28T22:05:35+00:00,2025-08-28T22:05:35+00:00,1,4,1,5,3,1,1,direct_commit,3.45,1,Edgar Emilio Vásquez Castillo,6
ONE-2973,🔀 ONE-2973 - Fix historical Automatic Payment when applying date filters. (pull request #1122),TB-Menu,2025-08-28T18:28:25+00:00,2025-08-28T18:28:25+00:00,1,0,9,9,-9,1,1,direct_commit,3.45,1,Julio Rico,6
ONE-2972,🔀 ONE-2972 - Fix historical Automatic Payment display issue on view first appear. (pull request #1121),TB-Menu,2025-08-28T16:06:28+00:00,2025-08-28T16:06:28+00:00,1,5,2,7,3,1,1,direct_commit,3.6,1,Julio Rico,6
ONE-2031,🔀 ONE-2031 - Fix Biometry alerts' body bullets alignment (pull request #1119),Components,2025-08-28T15:02:20+00:00,2025-08-28T15:02:20+00:00,1,140,57,197,83,10,1,direct_commit,37.85,1,Rodrigo Mejia,6
ONE-2981,🔀 ONE-2981 - Modify Credit card Text in AutoPayContractDetailView (pull request #1120),TB-Menu,2025-08-27T22:05:03+00:00,2025-08-27T22:05:03+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Javier Lorenzana,6
ONE-2864,🔀 ONE-2864 - Fix no DUI Warning bubble position (pull request #1117),Main,2025-08-27T22:04:16+00:00,2025-08-27T22:04:16+00:00,1,2,1,3,1,1,1,direct_commit,3.25,1,José Miguel Rivera López,6
ONE-1433,🔀 ONE-1433 - add hide data label to carousel card. (pull request #1111),Main,2025-08-27T17:55:14+00:00,2025-08-27T17:55:14+00:00,1,34,2,36,32,3,1,direct_commit,10.5,1,Javier Lorenzana,6
ONE-2760,🔀 ONE-2760 - Fix navigation back on Document Validation in General Data. (pull request #1115),TB-Menu,2025-08-27T17:54:46+00:00,2025-08-27T17:54:46+00:00,1,3,1,4,2,1,1,direct_commit,3.35,1,Javier Lorenzana,6
ONE-2828,🔀 ONE-2828 - Add new Selfie Scan Tutorials (pull request #1113),DesignSystem,2025-08-25T21:31:01+00:00,2025-08-25T21:31:01+00:00,1,1403,148,1551,1255,79,1,direct_commit,306.7,1,Josseh Blanco,6
ONE-2958,🔀 ONE-2958 - Fix card delivery date/time Selectables UI (pull request #1116),Components,2025-08-25T21:45:03+00:00,2025-08-25T21:45:03+00:00,1,101,18,119,83,5,1,direct_commit,22.0,1,Rodrigo Mejia,6
ONE-2827,🔀 ONE-2827 -  Include new target APIVersion property to support mulesoft and APIGateway url. (pull request #1107),Core,2025-08-25T16:44:51+00:00,2025-08-25T16:44:51+00:00,1,209,55,264,154,25,1,direct_commit,74.65,1,Emely Melgar,6
ONE-2955,🔀 ONE-2955 - Include app version for non productive environments. (pull request #1114),AC-EntryPoint,2025-08-22T20:52:43+00:00,2025-08-22T20:52:43+00:00,1,52,21,73,31,9,1,direct_commit,25.25,1,Emely Melgar,6
ONE-2916,🔀 ONE-2916 - Update the force update logic including the bundle version and build number. (pull request #1109),Core,2025-08-21T23:27:58+00:00,2025-08-21T23:27:58+00:00,1,246,47,293,199,9,1,direct_commit,45.95,1,Emely Melgar,6
ONE-2887,🔀 ONE-2887 - Replace the one app icon with a icon new version. (pull request #1110),DesignSystem,2025-08-21T19:57:38+00:00,2025-08-21T19:57:38+00:00,1,58,97,155,-39,42,1,direct_commit,95.65,1,Emely Melgar,6
ONE-2394,🔀 ONE-2394 - Add custom encoder/decoder for `OneFieldType` and `OneFieldDataType`. (pull request #1108),Core,2025-08-20T21:21:18+00:00,2025-08-20T21:21:18+00:00,1,12,0,12,12,1,1,direct_commit,4.2,1,Julio Rico,6
ONE-2883,🐛 ONE-2883 - Fix DateFormat in Rejection case (pull request #1104),OB-Onboarding,2025-08-20T15:33:20+00:00,2025-08-20T15:33:20+00:00,1,11,3,14,8,2,1,direct_commit,6.25,1,Javier Lorenzana,6
ONE-2922,✨ ONE-2922 - Implemente the state Z for users with overdue payments and hides the payment option in the card menu. (pull request #1105),Core,2025-08-19T15:28:25+00:00,2025-08-19T15:28:25+00:00,1,92,15,107,77,4,1,direct_commit,18.95,1,Emely Melgar,6
ONE-2907,"🐛 ONE-2907 - Remove close icon on Alert, add dot (.) at the end of the text (pull request #1101)",Main,2025-08-19T00:05:19+00:00,2025-08-19T00:05:19+00:00,1,2,2,4,0,2,1,direct_commit,5.3,1,Javier Lorenzana,6
ONE-2755,🔀 ONE-2755 - Update Endpoint name to /validate-forbidden-words (pull request #1100),Core,2025-08-13T20:16:41+00:00,2025-08-13T20:16:41+00:00,1,6,14,20,-8,1,2,direct_commit,5.3,1,José Miguel Rivera López,6
ONE-2821,🔀 ONE-2821 - Add missing Installment Value field in TicketContentView (PayInInstallments Flow) (pull request #1099),Main,2025-08-12T16:38:03-06:00,2025-08-13T19:16:49+00:00,1,70,10,80,60,3,4,direct_commit,17.5,1,Rodrigo Mejia,6
ONE-2369,🔀 ONE-2369 - Modify card cancellation dialog text (pull request #1096),Main,2025-08-12T19:36:46+00:00,2025-08-12T19:36:46+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Javier Lorenzana,6
ONE-2733,🔀 ONE-2733 - Add new cashback UX/UI (pull request #1089),TB-Home,2025-08-07T23:55:50+00:00,2025-08-07T23:55:50+00:00,1,1219,128,1347,1091,32,1,direct_commit,193.3,1,Rodrigo Mejia,6
ONE-2768,🚧 ONE-2768 - Add last files,Swift Code,2025-07-24T17:06:36+00:00,2025-08-07T17:39:19-06:00,14,215,181,396,34,24,6,direct_commit,84.55,2,Javier Lorenzana; Emely Melgar,168
ONE-2880,🔀 ONE-2880 - Update the empty state button text for protection plan screen. (pull request #1093),TB-Menu,2025-08-07T22:24:01+00:00,2025-08-07T22:24:01+00:00,1,12,1,13,11,2,1,direct_commit,6.25,1,Emely Melgar,6
ONE-2855,🐛 ONE-2855 - Fix management status picker reverting back to active after viewing a management detail (pull request #1091),TB-Menu,2025-08-07T20:11:25+00:00,2025-08-07T20:11:25+00:00,1,49,20,69,29,3,1,direct_commit,12.9,1,Josseh Blanco,6
ONE-2865,🔀 ONE-2865 - Fix textfield validation for additional card document form. (pull request #1090),Main,2025-08-07T20:09:07+00:00,2025-08-07T20:09:07+00:00,1,3,1,4,2,2,1,direct_commit,5.35,1,Emely Melgar,6
ONE-2873,🔀 ONE-2873 - Remove back button for disable confirmation screen. (pull request #1092),TB-Menu,2025-08-07T20:08:20+00:00,2025-08-07T20:08:20+00:00,1,8,1,9,7,2,1,direct_commit,5.85,1,Emely Melgar,6
ONE-2732,🔀 ONE-2732 - Fraud protection empty state UX improvement (pull request #1088),TB-Menu,2025-08-01T21:08:18+00:00,2025-08-01T21:08:18+00:00,1,243,210,453,33,17,1,direct_commit,69.8,1,Edgar Emilio Vásquez Castillo,6
ONE-2591,🔀 ONE-2591 - Add new dynatrace events for payments and point transfer in the card options menu (pull request #1087),TB-CreditCardMenu,2025-08-01T20:00:30+00:00,2025-08-01T20:00:30+00:00,1,280,2,282,278,10,1,direct_commit,49.1,1,Josseh Blanco,6
ONE-2837,🔀 ONE-2837 - Fix empty states for Card Pin. (pull request #1085),Core,2025-07-31T17:10:44+00:00,2025-07-31T17:10:44+00:00,1,10,1,11,9,2,1,direct_commit,6.05,1,Julio Rico,6
ONE-2587,🔀 ONE-2587 - Track new dynatrace events in Home and Main Menu (pull request #1086),Core,2025-07-31T16:57:07+00:00,2025-07-31T16:57:07+00:00,1,745,73,818,672,29,1,direct_commit,137.15,1,Josseh Blanco,6
ONE-2457,🔀 ONE-2457 - AFP - Hide back button on `SelfieScanView` (pull request #1080),OB-Onboarding,2025-07-30T23:13:36+00:00,2025-07-30T23:13:36+00:00,1,2,0,2,2,1,2,direct_commit,4.2,1,Julio Rico,6
INTO-1,🔀 Merged in sync/merge-1.17.1-into-1.18 (pull request #1082),Swift Code,2024-09-18T12:31:45-06:00,2025-07-31T15:41:23+00:00,315,2650,1671,4321,979,77,3,direct_commit,505.55,1,Emely Melgar,1890
ONE-2797,🔀 ONE-2797 - Fix missing UI update on additional card/third attempt flows (pull request #1079),Main,2025-07-25T11:21:01-06:00,2025-07-30T15:15:17+00:00,4,607,123,730,484,14,5,direct_commit,99.85,1,Rodrigo Mejia,24
ONE-2637,🔀 ONE-2637 - Update generic ups alert to support dismiss and custom action. (pull request #1076),OB-Onboarding,2025-07-29T21:41:56+00:00,2025-07-29T21:41:56+00:00,1,102,16,118,86,4,1,direct_commit,20.0,1,Emely Melgar,6
ONE-2629,🔀 ONE-2629 - Error when entering a previously registered phone number and then replacing it with a valid one (pull request #1078),Main,2025-07-24T11:41:23-06:00,2025-07-29T21:25:08+00:00,5,150,65,215,85,13,7,direct_commit,51.25,1,Javier Lorenzana,30
ONE-2801,🔀 ONE-2801 - Fix typo in empty state copy. (pull request #1073),TB-CreditCardMenu,2025-07-29T15:45:56+00:00,2025-07-29T15:45:56+00:00,1,63,1,64,62,3,1,direct_commit,13.35,1,Julio Rico,6
ONE-2589,🔀 ONE-2589 - Track Dynatrace events in the login and credential recovery flows (pull request #1072),AC-UserAuthentication,2025-07-29T00:00:50+00:00,2025-07-29T00:00:50+00:00,1,563,12,575,551,16,1,direct_commit,89.9,1,Josseh Blanco,6
ONE-2779,🔀ONE-2779 - Fix hide back button logic in main ticket view (pull request #1070),Main,2025-07-25T01:40:41+00:00,2025-07-25T01:40:41+00:00,1,4,1,5,3,1,1,direct_commit,3.45,1,Rodrigo Mejia,6
ONE-2774,🔀 ONE-2774 - Fix the logout request by updating the user session provider to send the authorization token. (pull request #1068),Core,2025-07-24T19:59:58+00:00,2025-07-24T19:59:58+00:00,1,8,5,13,3,2,1,direct_commit,6.05,1,Emely Melgar,6
ONE-2628,🔀 ONE-2628 - Add missing back on delivery address additional card flow (pull request #1063),Main,2025-07-24T17:50:41+00:00,2025-07-24T17:50:41+00:00,1,70,25,95,45,6,1,direct_commit,21.25,1,Edgar Emilio Vásquez Castillo,6
ONE-2767,Merged in feature/ONE-2767 (pull request #1064),Core,2025-07-24T15:57:05+00:00,2025-07-24T15:57:05+00:00,1,41,1252,1293,-1211,66,1,direct_commit,199.7,1,Emely Melgar,6
ONE-2044,🔀 ONE-2044 - Card Tracking Optimization (pull request #1046),TB-Menu,2025-07-23T22:42:05+00:00,2025-07-23T22:42:05+00:00,1,581,214,795,367,20,1,direct_commit,109.8,1,Javier Lorenzana,6
ONE-2579,🔀 ONE-2579 - Add new UI for delivery date/time selection (pull request #1060),Main,2025-07-15T15:27:35-06:00,2025-07-23T22:31:51+00:00,8,5083,2158,7241,2925,140,30,direct_commit,926.2,1,Rodrigo Mejia,48
ONE-2580,🔀 ONE-2580 - Improve Empty States in Credit Cards section (pull request #1057),TB-CreditCardMenu,2025-07-23T16:51:51+00:00,2025-07-23T16:51:51+00:00,1,376,109,485,267,27,1,direct_commit,98.05,1,Julio Rico,6
ONE-2585,🔀 ONE-2585 - Add new Dynatrace events for untracked screens in Onboarding (pull request #1061),OB-Onboarding,2025-07-22T22:13:04+00:00,2025-07-22T22:13:04+00:00,1,1103,140,1243,963,59,1,direct_commit,236.3,1,Josseh Blanco,6
ONE-2729,🔀 ONE-2729 - Fix bug with the $ sign (pull request #1058),TB-Menu,2025-07-18T18:11:25+00:00,2025-07-18T18:11:25+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Edgar Emilio Vásquez Castillo,6
ONE-2627,🔀 ONE-2627 - Update padding spacing and scroll area in Protection Plan view. (pull request #1059),TB-Menu,2025-07-15T14:58:45-06:00,2025-07-18T17:42:48+00:00,2,127,115,242,12,1,5,direct_commit,25.45,1,Julio Rico,12
ONE-2635,🔀 ONE-2635 - Reset the income repository each time the user returns from onboarding to prevent duplication. (pull request #1056),OB-Onboarding,2025-07-17T23:51:05+00:00,2025-07-17T23:51:05+00:00,1,12,0,12,12,2,1,direct_commit,6.2,1,Emely Melgar,6
SP-2421,🔀 ONE-SP-2421_ONE-2636 - Fix the logic to request transactions based on the selected state. (pull request #1054),Main,2025-07-11T15:29:06-06:00,2025-07-17T21:46:56+00:00,6,50,34,84,16,3,2,direct_commit,14.7,2,APPLAUDO; Emely Melgar,72
ONE-2636,🔀 ONE-SP-2421_ONE-2636 - Fix the logic to request transactions based on the selected state. (pull request #1054),Main,2025-07-17T21:46:56+00:00,2025-07-17T21:46:56+00:00,1,49,33,82,16,3,1,direct_commit,13.55,1,Emely Melgar,6
ONE-2268,🔀 ONE-2268 - Fix scrolling in user recovery screen (pull request #1044),AC-UserAuthentication,2025-07-17T15:33:34+00:00,2025-07-17T15:33:34+00:00,1,4,4,8,0,1,1,direct_commit,3.6,1,Rodrigo Mejia,6
ONE-2043,🔀 ONE-2043 - Terms and Conditions Optimization (pull request #1019),Main,2025-07-17T15:01:34+00:00,2025-07-17T15:01:34+00:00,1,541,295,836,246,17,1,direct_commit,103.85,1,José Miguel Rivera López,6
ONE-2264,🔀 ONE-2264 - Scrolling Problems - General Information - with Large Font Size (pull request #1036),TB-Menu,2025-07-16T22:07:22+00:00,2025-07-16T22:07:22+00:00,1,133,18,151,115,5,1,direct_commit,25.2,1,José Miguel Rivera López,6
ONE-2549,"🔀 ONE-2549 - Fix ""bug"" with disclaimer and icon in payment view (pull request #1049)",TB-Menu,2025-07-16T20:17:31+00:00,2025-07-16T20:17:31+00:00,1,19,9,28,10,7,1,direct_commit,17.35,1,Edgar Emilio Vásquez Castillo,6
ONE-2528,"⏪ Revert ""ONE-2528 - Deactivate the payment service option temporarily due to the release of version 1.18)"" (pull request #1053)",TB-Menu,2025-07-16T16:17:54+00:00,2025-07-16T19:26:28+00:00,1,4,5,9,-1,1,3,direct_commit,5.65,1,Emely Melgar,6
ONE-2522,🎨 ONE-2522 - Add final network connection logic proposal,TB-Menu,2025-06-25T14:53:06-06:00,2025-07-14T15:41:56-06:00,19,3762,1506,5268,2256,209,8,direct_commit,877.5,1,Rodrigo Mejia,114
ONE-2618,🔀 ONE-2618 - Remove back from survey result (pull request #1047),Main,2025-07-11T22:27:50+00:00,2025-07-11T22:27:50+00:00,1,4,2,6,2,4,1,direct_commit,9.5,1,Edgar Emilio Vásquez Castillo,6
ONE-2581,🔀 ONE-2581 - Fix Protection plan screen spacing. (pull request #1040),TB-Menu,2025-07-11T15:51:42+00:00,2025-07-11T15:51:42+00:00,1,17,13,30,4,1,1,direct_commit,5.35,1,Julio Rico,6
ONE-2386,🔀 ONE-2386 - Update text copy for recover user/update password (pull request #1039),Components,2025-07-08T15:36:37+00:00,2025-07-08T15:36:37+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Julio Rico,6
ONE-2263,🔀 ONE-2263 - Fix Scrolling Issues in Personal Information When Using Large Font Sizes (pull request #1016),OB-Onboarding,2025-07-08T15:35:54+00:00,2025-07-08T15:35:54+00:00,1,18,18,36,0,1,1,direct_commit,5.7,1,Javier Lorenzana,6
ONE-2042,🔀 ONE-2042 - Optimization in the User Activity Logs flow (pull request #1009),TB-Menu,2025-07-08T15:35:24+00:00,2025-07-08T15:35:24+00:00,1,760,329,1089,431,17,1,direct_commit,127.45,1,Javier Lorenzana,6
ONE-2371,🔀 ONE-2371 - Add customer support info support with remote notification.  (pull request #1034),Other,2025-07-08T15:07:38+00:00,2025-07-08T15:07:38+00:00,1,776,447,1223,329,85,1,direct_commit,270.95,1,Edgar Emilio Vásquez Castillo,6
ONE-2554,🔀 ONE-2554 - Fix NEP input invisible error label. (pull request #1038),TB-Menu,2025-07-07T22:55:03+00:00,2025-07-07T22:55:03+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Emely Melgar,6
ONE-2563,🔀 ONE-2563 - Update the placeholder for PuntoExpress forms to use the label instead of the tooltip property. (pull request #1037),TB-Menu,2025-07-07T20:10:11+00:00,2025-07-07T20:10:11+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Emely Melgar,6
ONE-2564,🔀 ONE-2564 - Update convenienceProductSelection logic to select the label according to the type. (pull request #1035),TB-Menu,2025-07-07T17:58:17+00:00,2025-07-07T17:58:17+00:00,1,49,18,67,31,3,1,direct_commit,12.8,1,Emely Melgar,6
ONE-2576,🔀 ONE-2576 - Fix stale income information checkpoint (pull request #1033),OB-Onboarding,2025-07-04T21:43:22+00:00,2025-07-04T21:43:22+00:00,1,5,110,115,-105,5,1,direct_commit,17.0,1,Josseh Blanco,6
ONE-2262,🔀 ONE-2262 - Fix syntax error. (pull request #1032),TB-Menu,2025-07-03T23:07:29+00:00,2025-07-04T17:41:16+00:00,1,63,54,117,9,4,2,direct_commit,19.0,2,Emely Melgar; Julio Rico,12
ONE-2265,🔀 ONE-2265 - Part 2: Add accessibility for larger fonts in Contacts section. (pull request #1013),AC-UserAuthentication,2025-07-03T00:29:49+00:00,2025-07-03T23:09:04+00:00,1,221,43,264,178,7,2,direct_commit,40.25,1,Julio Rico,6
ONE-2570,🔀 ONE-2570 - Show the megapoints/cashback flow only if available points has loaded successfully (pull request #1026),Components,2025-07-03T23:05:32+00:00,2025-07-03T23:05:32+00:00,1,27,6,33,21,5,1,direct_commit,14.0,1,Josseh Blanco,6
ONE-2512,🔀 ONE-2512 - Unintended back action on onboarding. (pull request #1021),Main,2025-07-03T20:29:53+00:00,2025-07-03T20:29:53+00:00,1,84,35,119,49,15,1,direct_commit,41.15,1,Edgar Emilio Vásquez Castillo,6
ONE-2507,🔀 ONE-2507 - Add the Address Name Type picker to the address selection form (pull request #1017),Main,2025-06-26T17:10:41+00:00,2025-07-03T17:04:18+00:00,6,211,42,253,169,12,2,direct_commit,49.2,1,Josseh Blanco,36
ONE-2266,🔀 ONE-2266 - Part 2: Add accessibility for larger fonts in Reset password section. (pull request #1020),AC-UserAuthentication,2025-07-02T18:55:18+00:00,2025-07-03T00:32:33+00:00,1,38,37,75,1,3,2,direct_commit,13.65,1,Julio Rico,6
ONE-2550,🔀 ONE-2550 - Rename the placeholder for the service payment dropdown. (pull request #1023),TB-Menu,2025-07-03T00:27:57+00:00,2025-07-03T00:27:57+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Emely Melgar,6
ONE-2504,🔀 ONE-2504 - Add an alert when the users attempt to save duplicated income information (pull request #1022),OB-Onboarding,2025-07-03T00:27:27+00:00,2025-07-03T00:27:27+00:00,1,113,7,120,106,6,1,direct_commit,24.65,1,Josseh Blanco,6
ONE-2532,🔀 ONE-2532 - Show menu alerts even if the biometric login sheet has been shown already (pull request #1012),TB-Home,2025-06-26T20:04:51+00:00,2025-06-26T20:04:51+00:00,1,11,1,12,10,1,1,direct_commit,4.15,1,Josseh Blanco,6
ONE-2452,🔀 ONE-2452 - Add the retry states in the Home screen (pull request #1008),TB-Home,2025-06-25T20:09:32+00:00,2025-06-25T20:09:32+00:00,1,303,98,401,205,13,1,direct_commit,62.2,1,Josseh Blanco,6
ONE-1417,🔀 ONE-1417 - Fix Lifemiles points transfer flow (pull request #1010),TB-CreditCardMenu,2025-06-10T08:59:01-06:00,2025-06-25T19:46:29+00:00,15,333,120,453,213,13,2,direct_commit,67.3,1,Rodrigo Mejia,90
ONE-2424,🔀 ONE-2424 - Managing unified endpoints LogIn (pull request #1006),Core,2025-06-24T16:10:20+00:00,2025-06-24T16:10:20+00:00,1,122,261,383,-139,15,1,direct_commit,56.25,1,José Miguel Rivera López,6
ONE-2514,🔀 ONE-2514 - Fix underlined cancel text in TrustedDevices Alert (pull request #1007),TB-Menu,2025-06-23T20:04:14+00:00,2025-06-23T20:04:14+00:00,1,2,2,4,0,2,1,direct_commit,5.3,1,Javier Lorenzana,6
ONE-2455,🔀 ONE-2455 - Retry state for skeleton effect in Main Menu and Credit Card Menu sections (pull request #1004),Components,2025-06-20T22:20:09+00:00,2025-06-20T22:20:09+00:00,1,161,78,239,83,15,1,direct_commit,51.0,1,Julio Rico,6
ONE-2312,🔀 ONE-2312 - Add missing items to match Android Add(pull request #1005),TB-Menu,2025-06-20T17:59:28+00:00,2025-06-20T17:59:28+00:00,1,26,1,27,25,4,1,direct_commit,11.65,1,Edgar Emilio Vásquez Castillo,6
ONE-2451,🔀 ONE-2451 - Add the shimmering effect to the Home View components (pull request #1002),TB-Home,2025-06-19T23:05:20+00:00,2025-06-19T23:05:20+00:00,1,208,139,347,69,11,1,direct_commit,50.75,1,Josseh Blanco,6
ONE-1703,🔀 ONE-1703 - Optimization in Trusted Devices List (pull request #991),TB-Menu,2025-06-19T20:48:53+00:00,2025-06-19T20:48:53+00:00,1,495,203,698,292,13,1,direct_commit,86.65,1,Javier Lorenzana,6
ONE-2387,🔀 ONE-2387 - Refactor the BillableAutomatic update form to support the notification channel and request. (pull request #1003),TB-Menu,2025-06-19T19:35:49+00:00,2025-06-19T19:35:49+00:00,1,199,93,292,106,11,1,direct_commit,47.55,1,Emely Melgar,6
ONE-2499,🔀 ONE-2499 - Fix UI discrepancies on Main Menu and Settings menu associated with ticket ONE-16949 (pull request #1001),Swift Code,2025-06-19T16:43:54+00:00,2025-06-19T16:43:54+00:00,1,2,1,3,1,2,1,direct_commit,5.25,1,Javier Lorenzana,6
ONE-16949,🔀 ONE-2499 - Fix UI discrepancies on Main Menu and Settings menu associated with ticket ONE-16949 (pull request #1001),Swift Code,2025-06-19T16:43:54+00:00,2025-06-19T16:43:54+00:00,1,2,1,3,1,2,1,direct_commit,5.25,1,Javier Lorenzana,6
ONE-2500,🔀 ONE-2500 - Fix reset password navigation Bug (pull request #998),TB-Menu,2025-06-18T21:15:56+00:00,2025-06-18T21:15:56+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Javier Lorenzana,6
ONE-1139,🔀 ONE-1139 - Set correct rejection screens on biometry step (OB) (pull request #997),Swift Code,2025-06-18T19:32:33+00:00,2025-06-18T19:32:33+00:00,1,11,94,105,-83,4,1,direct_commit,14.8,1,Rodrigo Mejia,6
ONE-2322,🔀 ONE-2322 - Add changes due the E2E Automatic Payment  (pull request #996),TB-Menu,2025-06-16T19:24:43+00:00,2025-06-16T19:24:43+00:00,1,193,118,311,75,33,1,direct_commit,92.2,1,Edgar Emilio Vásquez Castillo,6
ONE-2363,🔀 ONE-2363 - Add Skeleton effect loading state to Menu section. (pull request #986),Components,2025-06-13T19:42:29+00:00,2025-06-13T19:42:29+00:00,1,56,17,73,39,10,1,direct_commit,27.45,1,Julio Rico,6
ONE-2336,🔀 🐛 ONE-2336 - Set default selection card on Card Options View when the main card is not active (pull request #989),Core,2025-06-12T23:34:20+00:00,2025-06-12T23:34:20+00:00,1,253,5,258,248,6,1,direct_commit,38.55,1,Julio Rico,6
ONE-2472,🔀 ONE-2472 - Fix Checkpoint Screen for Complaints Flow (pull request #994),TB-Menu,2025-06-12T20:50:43+00:00,2025-06-12T20:50:43+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Javier Lorenzana,6
ONE-2348,🔀 ONE-2348 - Add native tab bar and optimize the home view (pull request #990),{Main => AC-UserAuthentication,2025-05-28T18:08:13-06:00,2025-06-12T19:57:53+00:00,14,7536,4797,12333,2739,393,34,direct_commit,1813.45,1,Josseh Blanco,84
ONE-2222,🔀 ONE-2222 - Fix view overflow on account statement flow (pull request #992),Main,2025-06-10T10:06:29-06:00,2025-06-12T16:25:03+00:00,2,263,95,358,168,5,4,direct_commit,45.05,1,Rodrigo Mejia,12
ONE-2467,🔀 ONE-2467 - Fix new selected installments options not being reflected on api call (pull request #993),Main,2025-06-12T16:22:11+00:00,2025-06-12T16:22:11+00:00,1,10,1,11,9,2,1,direct_commit,6.05,1,Rodrigo Mejia,6
ONE-1694,🔀 ONE-1694 - Optimization in Configurations Menu (pull request #975),TB-Menu,2025-05-29T14:54:40-06:00,2025-06-09T15:13:07+00:00,10,782,282,1064,500,18,2,direct_commit,130.3,2,Javier Lorenzana; Miguel Rivera,120
ONE-2364,🔀 ONE-2364 - Add Skeleton effect loading state to home cards. (pull request #977),Components,2025-06-05T22:21:47+00:00,2025-06-05T22:21:47+00:00,1,202,23,225,179,11,1,direct_commit,44.35,1,Julio Rico,6
ONE-2378,♻️ ONE-2378 - Refactor Login Response Data,Core,2025-05-29T15:45:13-06:00,2025-06-04T16:55:11-06:00,6,135,137,272,-2,7,3,direct_commit,37.35,1,Miguel Rivera,36
ONE-2376,♻️ ONE-2376 - Refactor Login Response Data,TB-Menu,2025-05-29T08:30:51-06:00,2025-06-04T16:15:26-06:00,6,19329,355,19684,18974,317,3,direct_commit,2587.65,1,Miguel Rivera,36
ONE-2377,♻️ ONE-2377 - Unify the Profile and GetLastLogin Endpoint,Core,2025-06-04T11:00:47-06:00,2025-06-04T11:00:47-06:00,1,48,41,89,7,6,1,direct_commit,19.85,1,Miguel Rivera,6
ONE-1658,🔀 ONE-1658 - Part2: Refactor the Dynatrace implementation to use parent actions instead of child actions. (pull request #973),OB-Onboarding,2025-05-29T19:52:30+00:00,2025-06-04T15:29:39+00:00,5,1333,1192,2525,141,143,2,direct_commit,480.9,1,Emely Melgar,30
ONE-2292,🔀 ONE-2292 - Migrate to new Amplitude Version (SPM) (pull request #978),Swift Code,2025-06-03T22:13:29+00:00,2025-06-03T22:13:29+00:00,1,119,84,203,35,9,1,direct_commit,35.1,1,Rodrigo Mejia,6
ONE-2306,🔀 ONE-2306 - Disable Input and Selectable explicitly on AutomaticPayment (pull request #974),TB-Menu,2025-06-02T21:29:47+00:00,2025-06-02T21:29:47+00:00,1,52,15,67,37,5,1,direct_commit,16.95,1,Edgar Emilio Vásquez Castillo,6
ONE-2305,🔀 ONE-2305 - Change FaceAuthentication and Fix PaymentPicker (pull request #970),TB-Menu,2025-05-27T15:55:50-06:00,2025-06-02T17:43:52+00:00,5,131,80,211,51,6,2,direct_commit,31.1,2,Emilio Vasquez; Edgar Emilio Vásquez Castillo,60
ONE-2358,♻️ ONE-2358 - Remove the associated CoordinatorTab value,Navigation,2025-05-30T11:44:51-06:00,2025-05-30T11:44:51-06:00,1,60,25,85,35,6,1,direct_commit,20.25,1,Josseh Blanco,6
ONE-2374,🔀 ONE-2374 - Use light theme for native bottom sheets (pull request #968),Components,2025-05-30T00:35:14+00:00,2025-05-30T00:35:14+00:00,1,84,9,93,75,5,1,direct_commit,19.85,1,Josseh Blanco,6
ONE-2092,🔀 ONE-2092 - Include Services Payment & Automatic Payment Features.  (pull request #967),TB-Menu,2025-05-27T22:41:54+00:00,2025-05-28T20:36:43+00:00,1,19739,261,20000,19478,297,2,direct_commit,2582.95,2,Emely Melgar; Josseh Blanco,12
ONE-2261,🔀 ONE-2261 - Fix scrolling behavior on card payment screen (pull request #964),TB-CreditCardMenu,2025-05-28T18:30:23+00:00,2025-05-28T18:30:23+00:00,1,81,15,96,66,4,1,direct_commit,17.85,1,Rodrigo Mejia,6
ONE-1798,🔀 ONE-1798 - Fix navigation issue with editing address in personal data section (pull request #963),TB-Menu,2025-05-27T18:01:43+00:00,2025-05-27T18:01:43+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Julio Rico,6
ONE-1800,🔀 ONE-1800 - Ensure credit card carousel retains visible card on navigation (pull request #962),Main,2025-05-27T16:54:57+00:00,2025-05-27T16:54:57+00:00,1,15,10,25,5,1,1,direct_commit,5.0,1,Julio Rico,6
ONE-1693,🔀 ONE-1693 - Optimization in Menu of the application (pull request #914),Swift Code,2025-05-27T15:04:20+00:00,2025-05-27T15:04:20+00:00,1,700,154,854,546,17,1,direct_commit,112.7,1,José Miguel Rivera López,6
ONE-1829,🔀 ONE-1829 - Include edit automatic services contract. (pull request #949),TB-Menu,2025-05-27T14:57:15+00:00,2025-05-27T14:57:15+00:00,1,1161,937,2098,224,47,1,direct_commit,257.95,1,Emely Melgar,6
ONE-1666,🔀 ONE-1666 - Update the UI for the customer references flow in onboarding (pull request #961),OB-Onboarding,2025-05-26T23:17:26+00:00,2025-05-26T23:17:26+00:00,1,1474,874,2348,600,32,1,direct_commit,256.1,1,Josseh Blanco,6
ONE-1563,🔀 ONE-1563 - Optimization in complaint handling flow (pull request #910),TB-Menu,2025-05-23T23:22:10+00:00,2025-05-23T23:22:10+00:00,1,2022,741,2763,1281,42,1,direct_commit,324.25,1,Javier Lorenzana,6
ONE-1828,🔀 ONE-1828 - Include cancel automatic services contract. (pull request #941),TB-Menu,2025-05-23T23:21:00+00:00,2025-05-23T23:21:00+00:00,1,547,65,612,482,20,1,direct_commit,98.95,1,Emely Melgar,6
ONE-1796,🐛 ONE-1796 - Additional card order fix for name and credit limit (pull request #955),Main,2025-05-23T23:19:02+00:00,2025-05-23T23:19:02+00:00,1,66,26,92,40,5,1,direct_commit,18.9,1,Julio Rico,6
ONE-2224,🔀 ONE-2224 - Add new max characters error message in address selection view  (pull request #956),Main,2025-05-23T23:18:27+00:00,2025-05-23T23:18:27+00:00,1,44,1,45,43,4,1,direct_commit,13.45,1,Rodrigo Mejia,6
HOTFIX-1,🔀 Merged in sync/hotfix-1.16.1-to-develop (pull request #960),Other,2024-04-01T12:21:31-06:00,2025-05-23T23:17:10+00:00,417,54264,75551,129815,-21287,2398,15,direct_commit,14014.95,5,jblanco-applaudostudios; Emilio Vasquez; Edgar Emilio Vásquez Castillo; Josseh Blanco; Emely Melgar,12510
SP-2350,🔀 ONEAPP-SP-2350: Document preview updated (pull request #957),Main,2025-05-23T23:00:31+00:00,2025-05-23T23:00:31+00:00,1,10,9,19,1,6,1,direct_commit,14.45,1,José De la O,6
RELEASE-1,🔀 Merged in sync-release-1.16-to-1.17 (pull request #959),Swift Code,2024-03-11T18:29:27-06:00,2025-05-23T22:50:56+00:00,437,20101,39253,59354,-19152,592,12,direct_commit,5168.75,3,jblanco-applaudostudios; Josseh Blanco; Emely Melgar,7866
TO-1,🔀 Merged in sync-release-1.16-to-1.17 (pull request #959),Core,2025-05-23T16:36:09-06:00,2025-05-23T22:50:56+00:00,1,68,8,76,60,10,2,direct_commit,29.2,1,Emely Melgar,6
ONE-1719,🔀 ONE-1719 - Add the edit and delete beneficiary functionalities (pull request #953),OB-Onboarding,2025-05-22T23:44:48+00:00,2025-05-22T23:44:48+00:00,1,242,25,267,217,13,1,direct_commit,52.45,1,Josseh Blanco,6
ONE-2068,🔀 ONE-2068 - Add new Pay In Installment flow (pull request #942),Main,2025-05-22T21:08:42+00:00,2025-05-22T21:08:42+00:00,1,1839,16,1855,1823,38,1,direct_commit,261.7,1,Rodrigo Mejia,6
ONE-2314,🔀 ONE-2314 - Localize strings and match to the figma (pull request #950),Main,2025-05-21T20:18:33+00:00,2025-05-21T20:18:33+00:00,1,36,3,39,33,2,1,direct_commit,8.75,1,Edgar Emilio Vásquez Castillo,6
ONE-2223,🔀 ONE-2223 - Add support for displaying Blocked and Repositioned Credit Cards in the Home Carousel (pull request #945),Main,2025-05-21T17:05:21+00:00,2025-05-21T17:05:21+00:00,1,568,221,789,347,16,1,direct_commit,100.85,1,Julio Rico,6
ONE-2287,🔀 ONE-2287 - Fix alerts that use the statusResponseError or statusRequestError (pull request #947),Core,2025-05-20T18:30:42+00:00,2025-05-20T18:30:42+00:00,1,29,12,41,17,3,1,direct_commit,10.5,1,Josseh Blanco,6
ONE-1827,🔀 ONE-1827 - Part 3 - Include automatic services payment feature. (pull request #938),TB-Menu,2025-05-02T14:14:41+00:00,2025-05-20T17:29:07+00:00,18,2685,1102,3787,1583,164,3,direct_commit,654.6,1,Emely Melgar,108
ONE-1716,🔀 ONE-1716 - Part 2: Add the new UI for the Accounts Beneficiaries View (pull request #943),Components,2025-05-16T15:03:56+00:00,2025-05-19T23:08:14+00:00,3,1618,561,2179,1057,43,2,direct_commit,277.85,1,Josseh Blanco,18
ONE-2279,🔀 ONE-2279 -  Set CUID valor explicity with customerID (pull request #940),Core,2025-05-19T17:34:30+00:00,2025-05-19T17:34:30+00:00,1,24,2,26,22,8,1,direct_commit,19.5,1,Edgar Emilio Vásquez Castillo,6
ONE-1803,🔀 ONE-1803 - Third Attempt Message Not Displayed After Sign-In (pull request #937),Main,2025-05-14T22:25:45+00:00,2025-05-14T22:25:45+00:00,1,1,0,1,1,1,1,direct_commit,3.1,1,Julio Rico,6
ONE-1825,🔀 ONE-1825 - Remove vertical white spaces in `AutoPayEnrollmentView`(pull request #936),TB-Menu,2025-04-08T20:53:15+00:00,2025-05-14T20:24:24+00:00,35,1452,80,1532,1372,30,3,direct_commit,212.2,1,Julio Rico,210
ONE-1799,🔀 ONE-1799 - Fix issue when updating phone number for personal references (pull request #934),TB-Menu,2025-05-12T16:37:38+00:00,2025-05-12T16:37:38+00:00,1,50,30,80,20,3,1,direct_commit,13.5,1,Julio Rico,6
ONE-1735,🔀 ONE-1735 - Display previously saved phone number in working place. (pull request #926),Components,2025-05-09T17:29:14+00:00,2025-05-09T17:29:14+00:00,1,126,5,131,121,7,1,direct_commit,27.85,1,Julio Rico,6
ONE-1772,🔀 ONE-1772 - Sync - AddBackgroundEmbeddedNavigation (pull request #933),Components,2025-03-04T00:25:16+00:00,2025-05-08T23:53:04+00:00,65,2304,289,2593,2015,54,9,direct_commit,361.85,1,Edgar Emilio Vásquez Castillo,390
ONE-2052,🔀 ONE-2052  -  Payment service - UI Improvements (pull request #928),TB-Menu,2025-05-08T15:03:39+00:00,2025-05-08T15:03:39+00:00,1,81,38,119,43,16,1,direct_commit,43.0,1,Edgar Emilio Vásquez Castillo,6
ONE-2212,🔀 ONE-2212 - Remove Appcenter Stage (pull request #929),Other,2025-05-07T00:26:08+00:00,2025-05-07T00:26:08+00:00,1,0,31,31,-31,1,1,direct_commit,4.55,1,Edgar Emilio Vásquez Castillo,6
ONE-2082,🔀 ONE-2082 - Update localized string for job info. (pull request #924),OB-Onboarding,2025-04-29T17:28:49+00:00,2025-04-29T17:28:49+00:00,1,12,1,13,11,2,1,direct_commit,6.25,1,Emely Melgar,6
ONE-2079,🔀 ONE-2079 - Add a loading message to the refresh session alert for authenticated users (pull request #923),Core,2025-04-29T16:53:44+00:00,2025-04-29T16:53:44+00:00,1,40,7,47,33,7,1,direct_commit,19.35,1,Josseh Blanco,6
SP-2322,🔀 ONEAPP-SP-2322: Card delivery address selection fixed. (pull request #920),OB-Onboarding,2025-04-29T16:24:50+00:00,2025-04-29T16:24:50+00:00,1,16,0,16,16,3,1,direct_commit,8.6,1,José De la O,6
ONE-858,🔀 ONE-858 - Hide expenses tab from tabview (pull request #925),Swift Code,2025-04-29T16:10:26+00:00,2025-04-29T16:10:26+00:00,1,5,5,10,0,1,1,direct_commit,3.75,1,Rodrigo Mejia,6
ONE-2046,♻️ ONE-2046 - Refactor and reorganize component `HomeView` and `HomeCardDetailView` to apply shimmering effect,TB-Home,2025-04-28T16:05:24-06:00,2025-04-28T16:06:27-06:00,1,202,47,249,155,6,2,direct_commit,36.55,1,Julio Rico,6
ONE-2078,🔀 ONE-2078 - Fix users being sent to the guest view despite having logged in before (pull request #921),Main,2025-04-28T21:14:29+00:00,2025-04-28T21:14:29+00:00,1,25,15,40,10,8,1,direct_commit,20.25,1,Josseh Blanco,6
ONE-1720,🔀 ONE-1720 - Mejoras ScanDUI UI/UX (pull request #917),Main,2025-04-28T17:30:27+00:00,2025-04-28T17:30:27+00:00,1,176,36,212,140,12,1,direct_commit,44.4,1,Edgar Emilio Vásquez Castillo,6
ONE-2076,🔀 ONE-2076 - Fix voicing in the refresh session error alert (pull request #918),Components,2025-04-25T16:07:23+00:00,2025-04-25T16:07:23+00:00,1,2,2,4,0,1,1,direct_commit,3.3,1,Josseh Blanco,6
ONE-1830,🔀 ONE-1830 - Payment history (pull request #907),TB-Menu,2025-04-24T23:38:58+00:00,2025-04-24T23:38:58+00:00,1,1640,18,1658,1622,46,1,direct_commit,257.9,1,Julio Rico,6
ONE-1964,🔀 ONE-1964 - Add the refresh session error alert (pull request #916),Core,2025-04-24T18:17:09+00:00,2025-04-24T18:17:09+00:00,1,556,62,618,494,28,1,direct_commit,115.7,1,Josseh Blanco,6
ONE-2032,🔀 ONE-2032 - Include help alert for workplace onboarding flow. (pull request #915),Swift Code,2025-04-23T22:22:49+00:00,2025-04-23T22:22:49+00:00,1,331,61,392,270,46,1,direct_commit,129.15,1,Emely Melgar,6
ONE-1907,🔀 ONE-1907 - Display error message as toast (pull request #912),Swift Code,2025-04-21T16:48:27+00:00,2025-04-21T16:48:27+00:00,1,8,5,13,3,1,1,direct_commit,4.05,1,Julio Rico,6
ONE-1876,🔀 ONE-1876 - Add new face validation alerts (pull request #909),Main,2025-04-16T19:11:54+00:00,2025-04-16T19:11:54+00:00,1,1330,1099,2429,231,20,1,direct_commit,228.95,1,Rodrigo Mejia,6
ONE-1965,🔀 ONE-1965 - Remove ExpiredSessionSingleton and manage the authenticated session with the UserSessionManager (pull request #908),Swift Code,2025-04-15T18:36:01+00:00,2025-04-15T18:36:01+00:00,1,830,848,1678,-18,40,1,direct_commit,206.4,1,Josseh Blanco,6
ONE-2000,"🔀 ONE-2000 - Components improvement (BillPaymentInfo, Button-Hidding and SwipeBack) (pull request #904)",TB-Menu,2025-04-14T22:03:45+00:00,2025-04-14T22:03:45+00:00,1,493,473,966,20,24,1,direct_commit,121.95,1,Edgar Emilio Vásquez Castillo,6
ONE-1866,🔀 ONE-1866 - Add new Map floater logic and UI (pull request #902),Main,2025-04-08T21:40:25+00:00,2025-04-08T21:40:25+00:00,1,67,17,84,50,5,1,direct_commit,18.55,1,Rodrigo Mejia,6
ONE-1823,🔀 ONE-1823 - Add automatic payment account affiliation form (pull request #881),TB-Menu,2025-04-08T16:34:20+00:00,2025-04-08T16:34:20+00:00,1,1083,18,1101,1065,29,1,direct_commit,168.2,1,Rodrigo Mejia,6
ONE-1820,🔀 ONE-1820 - Automatic payments and Empty state section (pull request #865),TB-Menu,2025-04-07T21:38:58+00:00,2025-04-07T21:38:58+00:00,1,945,14,959,931,25,1,direct_commit,146.2,1,Julio Rico,6
ONE-2004,🔀 ONE-2004 - Include the new zip without the default tutorial for FacePhi.  (pull request #901),Other,2025-04-04T18:17:02+00:00,2025-04-04T18:17:02+00:00,1,0,0,0,0,2,1,direct_commit,5.0,1,Emely Melgar,6
ONE-1944,🔀 ONE-1944 - Fix navigation routing for selfie and document scan. (pull request #900),Main,2025-04-04T16:31:18+00:00,2025-04-04T16:31:18+00:00,1,24,3,27,21,2,1,direct_commit,7.55,1,Emely Melgar,6
ONE-1904,🔀 ONE-1904 - New biometry error messages (pull request #893),OB-Onboarding,2025-04-03T23:40:13+00:00,2025-04-03T23:40:13+00:00,1,280,8,288,272,5,1,direct_commit,39.4,1,Julio Rico,6
ONE-1844,🔀 ONE-1844 - Move the PEP condition coordinator before the AFP flow (pull request #897),OB-Onboarding,2025-04-03T23:11:11+00:00,2025-04-03T23:11:11+00:00,1,484,343,827,141,32,1,direct_commit,130.55,1,Josseh Blanco,6
ONE-1963,🔀 ONE-1963 -  Add scrollview centered content (pull request #899),TB-Menu,2025-04-03T22:21:01+00:00,2025-04-03T22:21:01+00:00,1,98,57,155,41,9,1,direct_commit,31.65,1,Edgar Emilio Vásquez Castillo,6
ONE-1867,"🔀 ONE-1867 - Update alerts for FaceAuthentication, ForNickname and Extension (pull request #894)",Main,2025-04-03T20:21:04+00:00,2025-04-03T20:21:04+00:00,1,682,99,781,583,21,1,direct_commit,116.15,1,Rodrigo Mejia,6
ONE-1973,🔀 ONE-1973 - Remove Facephi logo form selfie and document widget. (pull request #898),Other,2025-04-03T18:31:52+00:00,2025-04-03T18:31:52+00:00,1,0,0,0,0,2,1,direct_commit,5.0,1,Emely Melgar,6
ONE-1961,🔀 ONE-1961 - Update the date format for the bill detail. (pull request #895),TB-Menu,2025-04-02T15:30:29+00:00,2025-04-02T15:30:29+00:00,1,3,2,5,1,1,1,direct_commit,3.4,1,Emely Melgar,6
ONE-1950,🔀 ONE-1950  - Add Error Alert on Checkpoint Payment (pull request #892),TB-Menu,2025-04-02T15:27:34+00:00,2025-04-02T15:27:34+00:00,1,117,7,124,110,6,1,direct_commit,25.05,1,Edgar Emilio Vásquez Castillo,6
ONE-1943,🔀 ONE-1943 - Fix users being stuck on the contract list in onboarding after an error,Main,2025-03-28T23:21:26+00:00,2025-03-28T23:21:26+00:00,1,10,5,15,5,3,1,direct_commit,8.25,1,Josseh Blanco,6
ONE-1862,🔀 ONE-1862 - Add documentation for ExpiredSessionSingleton and UserSessionManager (pull request #889),Core,2025-03-28T14:35:40+00:00,2025-03-28T14:35:40+00:00,1,117,3,120,114,2,1,direct_commit,16.85,1,Josseh Blanco,6
ONE-1774,🔀 ONE-1774- Include payment interactor and billable ticket. (pull request #888),TB-Menu,2025-02-25T17:29:56+00:00,2025-03-27T05:47:22+00:00,29,1188,105,1293,1083,40,2,direct_commit,206.05,1,Emely Melgar,174
ONE-1923,🔀 ONE-1923 - Change the check point template for the caapplicationst1 call (pull request #885),Main,2025-03-24T23:29:00+00:00,2025-03-24T23:29:00+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Josseh Blanco,6
ONE-1871,🔀 ONE-1871  - Upgrade FraudForce (pull request #884),Build System,2025-03-24T23:16:07+00:00,2025-03-24T23:16:07+00:00,1,5,5,10,0,2,1,direct_commit,5.75,1,Edgar Emilio Vásquez Castillo,6
SP-2273,🔀 ONEAPP-SP-2273 - New Selphi and SelphiID build updated.,Other,2025-03-24T21:40:28+00:00,2025-03-24T21:40:28+00:00,1,61816,58879,120695,2937,2764,1,direct_commit,14654.55,1,José De la O,6
ONE-1857,🔀 ONE-1857 - Add more detailed error descriptions for the dynatrace request/response logs (pull request #878),Core,2025-03-24T17:40:02+00:00,2025-03-24T17:40:02+00:00,1,623,47,670,576,17,1,direct_commit,99.65,1,Josseh Blanco,6
ONE-1655,"🔀 ONE-1655 & ONE-1903 - Fix calendar behavior in ""Mis Movimientos"" view (pull request #883)",Components,2025-03-21T19:50:25+00:00,2025-03-21T19:50:25+00:00,1,67,56,123,11,3,1,direct_commit,16.5,1,Rodrigo Mejia,6
ONE-1903,"🔀 ONE-1655 & ONE-1903 - Fix calendar behavior in ""Mis Movimientos"" view (pull request #883)",Components,2025-03-21T19:50:25+00:00,2025-03-21T19:50:25+00:00,1,67,56,123,11,3,1,direct_commit,16.5,1,Rodrigo Mejia,6
ONE-1872,🔀 ONE-1872 - Add SelectOne on Account Form (pull request #882),Swift Code,2025-03-21T15:10:34+00:00,2025-03-21T15:10:34+00:00,1,247,149,396,98,54,1,direct_commit,141.15,1,Edgar Emilio Vásquez Castillo,6
ONE-1555,🔀 ONE-1555 - Update Onboarding screens to match Figma design (pull request #826),OB-Onboarding,2025-03-19T18:11:19+00:00,2025-03-19T18:11:19+00:00,1,165,46,211,119,6,1,direct_commit,31.8,1,Julio Rico,6
ONE-1776,🔀 ONE-1776 - Fix Bill Dynamic form UI (pull request #880),TB-Menu,2025-03-06T23:30:48+00:00,2025-03-18T18:10:30+00:00,11,556,347,903,209,23,4,direct_commit,122.95,1,Rodrigo Mejia,66
ONE-1873,🔀 ONE-1873 - Remove type assignment recursion from session log list.  (pull request #879),Components,2025-03-18T17:38:24+00:00,2025-03-18T17:38:24+00:00,1,0,1,1,-1,1,1,direct_commit,3.05,1,Emely Melgar,6
ONE-1768,🔀  ONE-1768 - Fix animation and spacing for empty state (pull request #870),Configuration,2025-02-27T15:26:46+00:00,2025-03-17T21:00:09+00:00,18,4447,102,4549,4345,56,6,direct_commit,567.8,1,Julio Rico,108
ONE-1771,🔀 ONE-1771 - Update colector form to use form domains.  (pull request #872),TB-Menu,2025-02-22T01:54:24+00:00,2025-03-17T20:13:07+00:00,23,1331,413,1744,918,28,6,direct_commit,215.75,2,Vladimir Guevara; Emely Melgar,276
ONE-1679,🔀 ONE-1679- FixStages (pull request #836),Other,2025-02-19T22:19:12+00:00,2025-02-20T16:23:32+00:00,1,320,29698,30018,-29378,209,4,direct_commit,1938.9,1,Edgar Emilio Vásquez Castillo,6
ONE-1766,🔀 ONE-1766 - Remove unwanted animation and improve navigation logic (pull request #852),Components,2025-02-24T20:28:36+00:00,2025-03-04T16:40:40+00:00,7,2619,83,2702,2536,53,3,direct_commit,375.05,1,Rodrigo Mejia,42
ONE-1834,🔀 ONE-1834 - Update data in selector in Credit Card Transactions (pull request #850),Main,2025-02-28T20:22:59+00:00,2025-02-28T20:22:59+00:00,1,3,3,6,0,1,1,direct_commit,3.45,1,Julio Rico,6
ONE-1516,🔀 ONE-1516 - Remove onAppear extension and include NonSecureViewIdentifier. (pull request #840),Components,2025-02-18T16:54:25+00:00,2025-02-21T21:13:24+00:00,3,398,111,509,287,23,3,direct_commit,94.35,1,Emely Melgar,18
ONE-1801,🔀 ONE-1801 - Remove the disabled condition for Transactions option. (pull request #841),TB-Home,2025-02-21T18:46:56+00:00,2025-02-21T18:46:56+00:00,1,0,2,2,-2,1,1,direct_commit,3.1,1,Emely Melgar,6
ONE-1765,🚧 ONE-1765 - Include architecture files for BillableAccount (pull request #833),TB-Menu,2025-02-19T20:36:53+00:00,2025-02-19T20:36:53+00:00,1,403,5,408,398,13,1,direct_commit,67.55,1,Emely Melgar,6
ONE-1749,🔀 ONE-1749 - Fix issue with Tracking Card section (pull request #830),Core,2025-02-18T22:09:52+00:00,2025-02-18T22:09:52+00:00,1,4,2,6,2,2,1,direct_commit,5.5,1,Julio Rico,6
ONE-1763,🔀 ONE-1763 - Save Acceptance Status when users rejects terms and,Main,2025-02-18T21:54:49+00:00,2025-02-18T21:54:49+00:00,1,1,0,1,1,1,1,direct_commit,3.1,1,Vladimir Guevara,6
ONE-1751,🔀 ONE-1751 - Improve the text rendering for the Terms and FATCA sections. (pull request #829),Main,2025-02-18T19:51:35+00:00,2025-02-18T19:51:35+00:00,1,2,2,4,0,2,1,direct_commit,5.3,1,Julio Rico,6
ONE-1739,🔀 ONE-1739 - Refactor the protection plan contract to include the new services and the missing fraudProtectionID. (pull request #828),TB-Menu,2025-02-14T00:28:19+00:00,2025-02-14T00:28:19+00:00,1,61,79,140,-18,4,1,direct_commit,19.05,1,Emely Melgar,6
ONE-1556,🔀 ONE-1556 - Show the credit card holder's name in the selector within the Credit Card Options section (pull request #824),TB-CreditCardMenu,2025-02-12T20:14:16+00:00,2025-02-12T20:14:16+00:00,1,28,21,49,7,2,1,direct_commit,8.85,1,Julio Rico,6
ONE-1461,"🔀 ONE-1461 - Display credit card transactions, including those from blocked credit cards. (pull request #823)",Core,2025-02-12T15:15:08+00:00,2025-02-12T15:15:08+00:00,1,233,52,285,181,10,1,direct_commit,46.9,1,Julio Rico,6
ONE-1712,🔀 ONE-1712 - Re-add missing offers asset (pull request #822),DesignSystem,2025-02-11T16:01:35+00:00,2025-02-11T16:01:35+00:00,1,48,0,48,48,7,1,direct_commit,19.8,1,Rodrigo Mejia,6
ONE-1708,🔀 ONE-1708 - Fix navigation logic in additional card information flow (pull request #821),Main,2025-02-07T19:59:24+00:00,2025-02-07T19:59:24+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Rodrigo Mejia,6
ONE-1665,🔀 ONE-1665 - Update flag for back navigation (pull request #820),Components,2025-01-30T21:28:11+00:00,2025-02-07T15:47:38+00:00,7,8,3,11,5,2,2,direct_commit,6.95,1,Julio Rico,42
ONE-1707,🔀 ONE-1707 - Add missing force item value on the companies picker list (pull request #819),OB-Onboarding,2025-02-06T17:49:49+00:00,2025-02-06T17:49:49+00:00,1,13,11,24,2,1,1,direct_commit,4.85,1,Edgar Emilio Vásquez Castillo,6
SP-2231,Merged in support/bugfix/ONEAPP-SP-2231 (pull request #818),Other,2025-02-06T15:22:08+00:00,2025-02-06T15:22:08+00:00,1,0,0,0,0,1,1,direct_commit,3.0,1,José De la O,6
ONE-1664,🔀 ONE-1664 - Fix padding for error message in `OTPCredentialRecoveryInputView` (pull request #814),Swift Code,2025-02-05T16:08:36+00:00,2025-02-05T16:08:36+00:00,1,5,4,9,1,1,1,direct_commit,3.7,1,Julio Rico,6
ONE-1667,🔀ONE-1667 - Delete unused files from views folder and move unoptimized files to new file structure (pull request #816),Swift Code,2025-02-04T20:45:09+00:00,2025-02-04T20:45:09+00:00,1,287,6674,6961,-6387,166,1,direct_commit,695.4,1,Rodrigo Mejia,6
ONE-1547,"🔀 ONE-1547 - Redesign ""Contanos mas de vos"" screen (pull request #815)",OB-Onboarding,2025-02-03T17:37:28+00:00,2025-02-03T17:37:28+00:00,1,725,6,731,719,12,1,direct_commit,97.8,1,Vladimir Guevara,6
ONE-1545,🔀 ONE-1545 - Add new additional card information editing flow (pull request #809),Main,2025-02-01T02:25:16+00:00,2025-02-01T02:25:16+00:00,1,1214,105,1319,1109,25,1,direct_commit,177.65,1,Rodrigo Mejia,6
ONE-1531,🔀 ONE-1531 - Add live search support to the picker list with query search (pull request #810),DesignSystem,2025-01-31T16:02:47+00:00,2025-01-31T16:02:47+00:00,1,513,109,622,404,32,1,direct_commit,121.75,1,Edgar Emilio Vásquez Castillo,6
ONE-1683,🔀 ONE-1683 - Adjust Alignment of LoginPage header,Main,2025-01-31T15:39:40+00:00,2025-01-31T15:39:40+00:00,1,2,2,4,0,1,1,direct_commit,3.3,1,Vladimir Guevara,6
ONE-1657,🔀 ONE-1657 - Fix last item selected on `OneWheelPicker `  (pull request #808),Components,2025-01-30T17:49:06+00:00,2025-01-30T17:49:06+00:00,1,15,5,20,10,1,1,direct_commit,4.75,1,Julio Rico,6
ONE-1641,🔀 ONE-1641 - Adjust recoveryCredentialButton tappable area in LoginPage,DesignSystem,2025-01-27T20:29:04+00:00,2025-01-30T15:44:45+00:00,2,63,78,141,-15,7,2,direct_commit,26.2,1,Vladimir Guevara,12
ONE-1661,🔀 ONE-1661 - Change shortImage to contextImage. (pull request #807),Core,2025-01-29T14:54:27+00:00,2025-01-29T14:54:27+00:00,1,6,6,12,0,3,1,direct_commit,7.9,1,Emely Melgar,6
ONE-1485,🔀 ONE-1485 - Fix hiding banners in credit card menu (pull request #788),TB-CreditCardMenu,2025-01-29T02:17:30+00:00,2025-01-29T02:17:30+00:00,1,141,42,183,99,11,1,direct_commit,39.2,1,Julio Rico,6
ONE-1557,🔄 ONE-1557 - Remove unused files and move the deactivated modules into new path. (pull request #803),Swift Code,2025-01-28T16:47:28+00:00,2025-01-28T16:47:28+00:00,1,616,13392,14008,-12776,445,1,direct_commit,1622.2,1,Emely Melgar,6
ONE-1558,🔀 ONE-1558 - Update the error message to indicate that the document has expired. (pull request #805),Main,2025-01-27T21:52:19+00:00,2025-01-27T21:52:19+00:00,1,13,2,15,11,2,1,direct_commit,6.4,1,Julio Rico,6
ONE-1275,🔀 ONE-1275 - add new user banner content in LoginPage,DesignSystem,2025-01-23T21:05:52+00:00,2025-01-23T21:05:52+00:00,1,298,102,400,196,21,1,direct_commit,77.9,1,Vladimir Guevara,6
ONE-1247,🔀 ONE-1247 - Update URL Api request and rendering in view for display markdown text in Terms and Conditions section (pull request #802),Core,2025-01-23T20:54:19+00:00,2025-01-23T20:54:19+00:00,1,19,5,24,14,4,1,direct_commit,11.15,1,Julio Rico,6
ONE-1398,🔀 ONE-1398 - Add new parameter to apply mask on OneTextFieldConfiguration,Components,2025-01-23T16:52:25+00:00,2025-01-23T16:52:25+00:00,1,3,0,3,3,2,1,direct_commit,5.3,1,Vladimir Guevara,6
ONE-1601,🔀 ONE-1601 - Fix issue while transfer points on credit card on last step (pull request #799),TB-CreditCardMenu,2025-01-22T22:44:31+00:00,2025-01-22T22:44:31+00:00,1,5,4,9,1,1,1,direct_commit,3.7,1,Julio Rico,6
ONE-1499,🔀 ONE-1499 - Credit Card Points Transfer - Navigation Flow Update (pull request #797),TB-CreditCardMenu,2025-01-22T00:01:40+00:00,2025-01-22T00:01:40+00:00,1,75,40,115,35,7,1,direct_commit,24.5,1,Julio Rico,6
ONE-1521,💚 ONE-1521 - Isolate the firebase  distribution step (pull request #798),Other,2025-01-21T23:12:57+00:00,2025-01-21T23:12:57+00:00,1,24,2,26,22,1,1,direct_commit,5.5,1,Edgar Emilio Vásquez Castillo,6
ONE-1511,🔀 ONE-1511 & ONE1521 - Implement Firebase Distribution(pull request #796),Other,2025-01-21T20:46:55+00:00,2025-01-21T20:46:55+00:00,1,44,0,44,44,3,1,direct_commit,11.4,1,Edgar Emilio Vásquez Castillo,6
ONE-1432,🔀 ONE-1432 - Disable animated navigation for cardProcess,Swift Code,2025-01-21T20:18:40+00:00,2025-01-21T20:18:40+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Vladimir Guevara,6
ONE-1466,🔀ONE-1466 - Remove unused image assets from Xcode project  (pull request #793),Other,2025-01-17T17:15:17+00:00,2025-01-17T17:15:17+00:00,1,21,3068,3089,-3047,345,1,direct_commit,846.5,1,Rodrigo Mejia,6
ONE-1552,♻️ ONE-1552 - Recovery custom navigation delete items. (pull request #790),Swift Code,2025-01-16T22:05:59+00:00,2025-01-16T22:05:59+00:00,1,30,0,30,30,1,1,direct_commit,6.0,1,Emely Melgar,6
ONE-1401,🔀 ONE-1401 - Removing dead code (pull request #789),Swift Code,2025-01-16T20:18:08+00:00,2025-01-16T20:18:08+00:00,1,864,24082,24946,-23218,447,1,direct_commit,2185.5,1,Edgar Emilio Vásquez Castillo,6
ONE-1529,🔀 ONE-1529 - Handling error scenario cancel a credit card (pull request #787),Main,2025-01-15T18:42:09+00:00,2025-01-15T18:42:09+00:00,1,3,1,4,2,1,1,direct_commit,3.35,1,Julio Rico,6
ONE-1422,🔀 ONE-1422 - PART 1: Remove unused files. (pull request #786),Swift Code,2025-01-14T23:12:46+00:00,2025-01-14T23:12:46+00:00,1,411,4556,4967,-4145,398,1,direct_commit,1065.9,1,Emely Melgar,6
ONE-1500,🔀ONE-1500 - Add missing max beneficiaries disclaimer message (pull request #785),OB-Onboarding,2025-01-14T20:56:18+00:00,2025-01-14T20:56:18+00:00,1,62,21,83,41,4,1,direct_commit,16.25,1,Rodrigo Mejia,6
ONE-1526,🔀 ONE-1526 - Fix message text for Complain section (pull request #783),TB-Menu,2025-01-10T21:15:26+00:00,2025-01-10T21:15:26+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Julio Rico,6
ONE-1484,🔀 ONE-1484 - Enable credit card menu options for blocked credit cards (pull request #782),Swift Code,2025-01-10T15:44:11+00:00,2025-01-10T15:44:11+00:00,1,1,89,90,-88,7,1,direct_commit,19.55,1,Julio Rico,6
ONE-1413,🔀 ONE-1413 - Update payment options for blocked credit cards (pull request #781),TB-CreditCardMenu,2024-12-19T17:07:27+00:00,2025-01-10T15:19:43+00:00,21,1661,435,2096,1226,26,3,direct_commit,242.85,1,Julio Rico,126
ONE-1509,🔀 ONE-1509 - Change voice tone for user unrecognized alert,OB-Onboarding,2025-01-08T20:39:36+00:00,2025-01-08T20:39:36+00:00,1,85,12,97,73,2,1,direct_commit,14.1,1,Vladimir Guevara,6
ONE-1508,🔀 ONE-1508 - Add locale to the converters and extensions (pull request #779),Swift Code,2025-01-08T16:09:24+00:00,2025-01-08T16:09:24+00:00,1,34,46,80,-12,9,1,direct_commit,24.7,1,Edgar Emilio Vásquez Castillo,6
ONE-904,🔀 ONE-904 - Update text values for menu section (pull request #778),TB-Menu,2025-01-08T15:47:15+00:00,2025-01-08T15:47:15+00:00,1,551,54,605,497,14,1,direct_commit,86.8,1,Julio Rico,6
ONE-1505,🔀 ONE-1505 - Fix wrong error handling on otp resend action (pull request #777),Swift Code,2025-01-06T21:14:28+00:00,2025-01-06T21:14:28+00:00,1,46,8,54,38,5,1,direct_commit,16.0,1,Rodrigo Mejia,6
ONE-1392,🐛 ONE-1392 - Disable continue before option selection on survey flow (pull request #776),Main,2025-01-06T16:47:38+00:00,2025-01-06T16:47:38+00:00,1,7,0,7,7,2,1,direct_commit,5.7,1,Rodrigo Mejia,6
ONE-1490,🔀 ONE-1490 - Fix wrong parameters in password reset OTP validation call (pull request #775),Swift Code,2025-01-06T14:43:13+00:00,2025-01-06T14:43:13+00:00,1,3,3,6,0,1,1,direct_commit,3.45,1,Rodrigo Mejia,6
ONE-911,🔀 ONE-911 - Change voice intonation an alert types in LoginPageAlerts,Main,2025-01-03T21:15:01+00:00,2025-01-03T21:15:01+00:00,1,128,17,145,111,4,1,direct_commit,22.65,1,Vladimir Guevara,6
ONE-1498,🔀 ONE-1498 - ONEAPP-SP-2169 - Receive memberId as String (pull request #768),Core,2025-01-03T00:16:35+00:00,2025-01-03T00:16:35+00:00,1,4,5,9,-1,4,1,direct_commit,9.65,1,José De la O,6
SP-2169,🔀 ONE-1498 - ONEAPP-SP-2169 - Receive memberId as String (pull request #768),Core,2025-01-03T00:16:35+00:00,2025-01-03T00:16:35+00:00,1,4,5,9,-1,4,1,direct_commit,9.65,1,José De la O,6
ONE-1492,🔀 ONE-1492 & ONE 1493 - Add resend toast and max attempts handling on OTP flows (pull request #772),Swift Code,2025-01-03T00:08:24+00:00,2025-01-03T00:08:24+00:00,1,18,4,22,14,5,1,direct_commit,13.0,1,Rodrigo Mejia,6
ONE-1470,🔀 ONE-1470_ONE 1471 -  Fix the card payment method defects.  (pull request #773),MenuCard,2024-12-23T16:38:08+00:00,2025-01-02T23:09:43+00:00,10,72,42,114,30,6,2,direct_commit,23.3,2,Edgar Emilio Vásquez Castillo; Emely Melgar,120
ONE-1393,🔀 ONE-1393 -  Display alert for changing password after using temporary credentials,Components,2025-01-02T21:19:01+00:00,2025-01-02T21:19:01+00:00,1,216,17,233,199,9,1,direct_commit,41.45,1,Julio Rico,6
ONE-1469,Merged in bugfix/ONE-1469 (pull request #765),TB-CreditCardMenu,2025-01-02T20:26:47+00:00,2025-01-02T20:26:47+00:00,1,18,5,23,13,2,1,direct_commit,7.05,1,Edgar Emilio Vásquez Castillo,6
ONE-1391,🔀 ONE-1391 - Send Notification for credential alert presentation,Main,2024-12-31T20:28:11+00:00,2024-12-31T20:28:11+00:00,1,34,3,37,31,6,1,direct_commit,16.55,1,Vladimir Guevara,6
ONE-1390,🔀 ONE-1390 - Onboarding Survey is not sorting correctly answers,Swift Code,2024-12-31T04:21:39+00:00,2024-12-31T04:21:39+00:00,1,220,4,224,216,10,1,direct_commit,43.2,1,Julio Rico,6
ONE-1448,🔀 ONE-1448 - Add missing email property on ccast1 call (pull request #767),Swift Code,2024-12-30T20:50:55+00:00,2024-12-30T22:57:37+00:00,1,8,6,14,2,1,2,direct_commit,5.1,1,Rodrigo Mejia,6
ONE-1303,"🔀 ONE-1303 & ONE-1305 - Add new OTP UI and logic for sms, WhatsApp and email codes (pull request #753)",Swift Code,2024-12-26T21:07:28+00:00,2024-12-26T21:07:28+00:00,1,3782,1664,5446,2118,78,1,direct_commit,618.4,1,Rodrigo Mejia,6
ONE-1305,"🔀 ONE-1303 & ONE-1305 - Add new OTP UI and logic for sms, WhatsApp and email codes (pull request #753)",Swift Code,2024-12-26T21:07:28+00:00,2024-12-26T21:07:28+00:00,1,3782,1664,5446,2118,78,1,direct_commit,618.4,1,Rodrigo Mejia,6
ONE-1405,🔀 ONE-1405 - Add new method in CivilStatusInteractor to navigate directly to user housing,OB-Onboarding,2024-12-26T15:51:46+00:00,2024-12-26T15:51:46+00:00,1,32,13,45,19,8,1,direct_commit,20.85,1,Vladimir Guevara,6
ONE-1454,🔀 ONE-1454 - Modifies third delivery attempt failed alert button title,Swift Code,2024-12-19T16:26:07+00:00,2024-12-26T15:51:09+00:00,6,18,6,24,12,3,2,direct_commit,10.1,1,Vladimir Guevara,36
ONE-1482,🔀 ONE-1482 - Fix text for protection plan (pull request #758),Main,2024-12-24T16:44:16+00:00,2024-12-24T16:44:16+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Julio Rico,6
ONE-1415,🔀ONE-1415 - Fix no action on back tap on additional card document scan (pull request #757),Main,2024-12-24T15:49:54+00:00,2024-12-24T15:49:54+00:00,1,3,1,4,2,1,1,direct_commit,3.35,1,Rodrigo Mejia,6
ONE-1472,🔀 ONE-1472 - Include missing protection plan text. (pull request #762),TB-Menu,2024-12-20T16:59:32+00:00,2024-12-23T23:46:01+00:00,3,211,162,373,49,14,2,direct_commit,59.2,1,Emely Melgar,18
ONE-1457,🔀 ONE-1457 - Solve bug with apperance of the navigation bar (pull request #756),Components,2024-12-17T20:42:18+00:00,2024-12-20T20:38:28+00:00,2,133,31,164,102,10,2,direct_commit,36.85,1,Edgar Emilio Vásquez Castillo,12
ONE-1456,🔀 ONE-1456_ONE-1472 - Fix protection plan UI and request issues. (pull request #754),TB-Menu,2024-12-20T16:59:32+00:00,2024-12-20T16:59:32+00:00,1,199,161,360,38,14,1,direct_commit,56.95,1,Emely Melgar,6
ONE-1411,🔀 ONE-1411  - Add OnHold Event when the offer is Pending (pull request #752),OB-Onboarding,2024-12-19T16:10:55+00:00,2024-12-19T16:10:55+00:00,1,19,1,20,18,4,1,direct_commit,10.95,1,Edgar Emilio Vásquez Castillo,6
ONE-1460,🔀 ONE-1460 - Fix message for blocked card,TB-CreditCardMenu,2024-12-18T13:37:59+00:00,2024-12-18T13:37:59+00:00,1,72,6,78,66,2,1,direct_commit,12.5,1,Julio Rico,6
ONE-1435,🔀 ONE-1435 - Add new frame for card credit limit presentation in CreditCardOfferDetailsView,OB-Onboarding,2024-12-12T15:16:00+00:00,2024-12-12T15:16:00+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Vladimir Guevara,6
ONE-903,🔀 ONE-903 - Add localization to Credit Cards (pull request #746),Main,2024-12-11T20:10:41+00:00,2024-12-11T20:10:41+00:00,1,595,62,657,533,16,1,direct_commit,95.6,1,Julio Rico,6
ONE-1400,🔀 ONE-1400 - Sort Atlantida folder root files by type. (pull request #743),Swift Code,2024-12-05T00:33:53+00:00,2024-12-05T00:33:53+00:00,1,39,31,70,8,1238,1,direct_commit,2482.45,1,Emely Melgar,6
ONE-1397,🔀 ONE-1397- Add new repository specs for cocoapods dependencies,Build System,2024-12-04T17:53:51+00:00,2024-12-04T17:53:51+00:00,1,10,10,20,0,1,1,direct_commit,4.5,1,Vladimir Guevara,6
ONE-902,🔀 ONE-902 - Add localization to Home section (pull request #741),Main,2024-12-04T15:30:58+00:00,2024-12-04T15:30:58+00:00,1,671,64,735,607,18,1,direct_commit,107.3,1,Julio Rico,6
ONE-1410,🔀 ONE-1410 - Include a new validation to clear the DUIAuth header only if the request is successful. (pull request #740),Core,2024-12-03T19:54:22+00:00,2024-12-03T19:54:22+00:00,1,28,16,44,12,2,1,direct_commit,8.6,1,Emely Melgar,6
ONE-507,🔀 ONE-507 - Implement Empty State UI for Card Options and Card Pin Change,Main,2024-12-03T03:12:07+00:00,2024-12-03T03:12:07+00:00,1,677,233,910,444,18,1,direct_commit,116.35,1,Vladimir Guevara,6
ONE-1329,🔀 ONE-1329 - Update transaction's name in movements section (pull request #735),Main,2024-12-02T21:13:15+00:00,2024-12-02T21:13:15+00:00,1,143,10,153,133,5,1,direct_commit,25.8,1,Julio Rico,6
ONE-1268,🔀 ONE-1268 - Update transaction's name in loyalty section (pull request #734),Main,2024-12-02T20:19:02+00:00,2024-12-02T20:19:02+00:00,1,106,10,116,96,6,1,direct_commit,24.1,1,Julio Rico,6
ONE-1235,🔀 ONE-1235 - Add toolbar configuration for main navigation,DesignSystem,2024-11-29T23:32:47+00:00,2024-11-29T23:32:47+00:00,1,597,4,601,593,34,1,direct_commit,128.9,1,Vladimir Guevara,6
ONE-1388,🔀 ONE-1388 - Move the isLoading position to hidden when an error or success occurs. (pull request #737),Components,2024-11-29T18:34:19+00:00,2024-11-29T18:34:19+00:00,1,2,1,3,1,1,1,direct_commit,3.25,1,Emely Melgar,6
ONE-1288,🔀 ONE-1288 - Include the Dynatraces custom actions. (pull request #728),Onboarding,2024-11-28T23:52:48+00:00,2024-11-28T23:52:48+00:00,1,1710,392,2102,1318,115,1,direct_commit,421.6,1,Emely Melgar,6
ONE-1384,🔀 ONE-1384 - Include the missing remote config parameter to decode the response. (pull request #732),Core,2024-11-27T21:25:00+00:00,2024-11-27T21:25:00+00:00,1,2,1,3,1,1,1,direct_commit,3.25,1,Emely Melgar,6
ONE-1299,🔀 ONE-1299 -  Cherry Pick - Add Analytic flag for remote configure (pull request #731),Swift Code,2024-11-27T16:55:50+00:00,2024-11-27T16:55:50+00:00,1,20,13,33,7,8,1,direct_commit,19.65,1,Emely Melgar,6
SP-2112,🔀 ONE-SP-2112 - New endpoint IdentificationImageAdd implemented (pull request #724),Core,2024-11-27T16:53:46+00:00,2024-11-27T16:53:46+00:00,1,259,28,287,231,9,1,direct_commit,46.3,1,José De la O,6
ONE-1345,🔀 ONE-1345 - Add missing identity validation checkpoint to user recovery flow (pull request #730),Components,2024-11-26T14:54:58+00:00,2024-11-26T17:50:37+00:00,1,164,30,194,134,8,2,direct_commit,35.9,1,Rodrigo Mejia,6
ONE-1335,🔀 ONE-1335 - Error while requesting an additional credit card (pull request #727),AdditionalCard,2024-11-25T20:09:39+00:00,2024-11-25T20:09:39+00:00,1,2,6,8,-4,2,1,direct_commit,5.5,1,Julio Rico,6
ONE-1336,🔀 ONE-1336 - Use new face auth endpoint for password recovery from login screen (pull request #725),Onboarding,2024-11-25T17:47:50+00:00,2024-11-25T17:47:50+00:00,1,162,30,192,132,17,1,direct_commit,52.7,1,Rodrigo Mejia,6
SP-2028,🔀 ONEAPP-SP-2028 - Validation modified for CardPayment Process (pull request #720),MenuCard,2024-11-22T23:19:08+00:00,2024-11-22T23:19:08+00:00,1,16,8,24,8,1,1,direct_commit,5.0,1,José De la O,6
ONE-1284,🔀 ONE-1284 - [iOS] Update endpoint model for contact client and  Account Beneficiaries (pull request #717),Core,2024-11-22T21:19:26+00:00,2024-11-22T21:19:26+00:00,1,3117,454,3571,2663,104,1,direct_commit,543.4,1,Julio Rico,6
ONE-1278,🔀 ONE-1278 & ONE-1279 - Fix UI and add new labels for document input in password and user recovery flow (pull request #722),Components,2024-11-22T20:17:05+00:00,2024-11-22T20:17:05+00:00,1,171,136,307,35,3,1,direct_commit,30.9,1,Rodrigo Mejia,6
ONE-1279,🔀 ONE-1278 & ONE-1279 - Fix UI and add new labels for document input in password and user recovery flow (pull request #722),Components,2024-11-22T20:17:05+00:00,2024-11-22T20:17:05+00:00,1,171,136,307,35,3,1,direct_commit,30.9,1,Rodrigo Mejia,6
ONE-1297,🔀 ONE-1297 - Set location marker when is coming back from the next view,Main,2024-11-14T00:43:46+00:00,2024-11-14T00:43:46+00:00,1,8,5,13,3,1,1,direct_commit,4.05,1,Vladimir Guevara,6
ONE-1277,🔀 ONE-1277 - Fix for Biometric Sheet for Touch ID and Face ID (pull request #707),Main,2024-11-11T20:40:28+00:00,2024-11-11T20:40:28+00:00,1,209,36,245,173,8,1,direct_commit,39.7,1,Julio Rico,6
ONE-1130,🔀 ONE-1130 - Add Appsflyer Events (pull request #709),Core,2024-11-11T20:37:19+00:00,2024-11-11T20:37:19+00:00,1,885,159,1044,726,35,1,direct_commit,167.45,1,Edgar Emilio Vásquez Castillo,6
ONE-509,🔀 ONE-509 - Builds Adress Selection Form Interactor,Main,2024-11-11T15:33:06+00:00,2024-11-11T15:33:06+00:00,1,258,143,401,115,19,1,direct_commit,71.95,1,Vladimir Guevara,6
ONE-1129,🔀 ONE-1129 - Add localization string for scan tutorials (pull request #706),Identity,2024-11-08T21:51:36+00:00,2024-11-08T21:51:36+00:00,1,166,16,182,150,5,1,direct_commit,28.4,1,Julio Rico,6
ONE-1138,🔀 ONE-1138 - Fix lifemiles points transfer ticker button configuration (pull request #710),MenuCard,2024-11-08T21:19:52+00:00,2024-11-08T21:19:52+00:00,1,9,4,13,5,1,1,direct_commit,4.1,1,Rodrigo Mejia,6
ONE-1265,🔀 ONE-1265 - Fix Labels of inputs in Profile references flow (pull request #705),Menu,2024-11-06T23:20:59+00:00,2024-11-06T23:20:59+00:00,1,112,13,125,99,4,1,direct_commit,20.85,1,Rodrigo Mejia,6
ONE-538,🔀 ONE-538 - Biometric Log in on fresh install (pull request #696),Swift Code,2024-11-05T18:03:16+00:00,2024-11-05T18:03:16+00:00,1,1600,168,1768,1432,40,1,direct_commit,249.4,1,Julio Rico,6
ONE-1242,🔀 ONE-1242 - Remove dashes in the phone number value. (pull request #703),Onboarding,2024-11-05T16:28:31+00:00,2024-11-05T16:28:31+00:00,1,26,16,42,10,15,1,direct_commit,34.4,1,Emely Melgar,6
ONE-1260,🔀 ONE-1260 - Fix typo and add new localizations to Profile contacts info flow (pull request #704),Core,2024-11-05T15:38:07+00:00,2024-11-05T15:38:07+00:00,1,42,2,44,40,3,1,direct_commit,11.3,1,Rodrigo Mejia,6
ONE-862,🔀 ONE-862 - Add missing disclaimer to workplace view (pull request #702),Onboarding,2024-11-04T22:21:51+00:00,2024-11-04T22:21:51+00:00,1,29,9,38,20,2,1,direct_commit,8.35,1,Rodrigo Mejia,6
ONE-1259,🔀 ONE-1259 - Revert CreditCardOfferCoordinator Creation in SplashRoute,Navigation,2024-11-04T21:39:56+00:00,2024-11-04T21:39:56+00:00,1,2,2,4,0,1,1,direct_commit,3.3,1,Vladimir Guevara,6
ONE-1241,🔀 ONE-1241 - Improve validateUser call for password reset flows (pull request #700),Components,2024-10-30T22:19:06+00:00,2024-10-30T22:19:06+00:00,1,51,27,78,24,4,1,direct_commit,15.45,1,Rodrigo Mejia,6
ONE-977,🔀 ONE-977 - Use auto validated phone number field on personal data flows (pull request #692),Onboarding,2024-10-30T22:18:11+00:00,2024-10-30T22:18:11+00:00,1,318,233,551,85,18,1,direct_commit,80.45,1,Rodrigo Mejia,6
ONE-1229,Merged in bugfix/ONE-1229 (pull request #695),Core,2024-10-30T22:02:02+00:00,2024-10-30T22:02:02+00:00,1,4,6,10,-2,2,1,direct_commit,5.7,1,Vladimir Guevara,6
ONE-985,Merged in feature/ONE-985 (pull request #693),Onboarding,2024-10-30T22:01:37+00:00,2024-10-30T22:01:37+00:00,1,102,53,155,49,4,1,direct_commit,21.85,1,Vladimir Guevara,6
ONE-1252,🔀 ONE-1252 - Update fastlane version to 2.225.0 to fix CI error (pull request #699),Other,2024-10-30T17:18:53+00:00,2024-10-30T17:18:53+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Emely Melgar,6
ONE-983,🔀 ONE-983 - Include workaround to support long press gesture. (pull request #697),Components,2024-10-16T23:34:00+00:00,2024-10-28T15:21:39+00:00,11,1599,244,1843,1355,43,2,direct_commit,260.1,1,Emely Melgar,66
ONE-1133,🔀 bugfix/ONE-1133-DataSaved (pull request #688),Swift Code,2024-10-22T22:45:08+00:00,2024-10-23T19:08:45+00:00,1,201,138,339,63,5,2,direct_commit,39.0,1,Edgar Emilio Vásquez Castillo,6
ONE-1132,🔀 ONE-1132 - Include educational level missing validation. (pull request #686),Onboarding,2024-10-22T22:04:50+00:00,2024-10-22T22:04:50+00:00,1,320,311,631,9,16,1,direct_commit,80.55,1,Emely Melgar,6
ONE-976,🔀 ONE-976 -  Add address names for card managements,Menu,2024-10-18T03:17:59+00:00,2024-10-18T03:17:59+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Vladimir Guevara,6
ONE-973,🔀 ONE-973 -  Add address names in Onboarding,Swift Code,2024-10-17T20:12:51+00:00,2024-10-17T20:12:51+00:00,1,326,102,428,224,11,1,direct_commit,60.7,1,Vladimir Guevara,6
ONE-981,🔀 ONE-981 - Add biometric validation to the cancellation card flow. (pull request #681),Main,2024-10-17T16:50:27+00:00,2024-10-17T16:50:27+00:00,1,1175,562,1737,613,39,1,direct_commit,224.6,1,Emely Melgar,6
ONE-648,✨ONE-648 - Onboarding - Financial products with other banks (pull request #679),Onboarding,2024-10-17T13:38:23+00:00,2024-10-17T13:38:23+00:00,1,953,415,1368,538,39,1,direct_commit,195.05,1,Julio Rico,6
ONEAPP-1310,🔀 ONEAPP-1310 - Crate CreditCardOfferDetailsVew component,Onboarding,2024-10-16T22:02:08+00:00,2024-10-16T22:02:08+00:00,1,863,549,1412,314,35,1,direct_commit,184.75,1,Vladimir Guevara,6
ONE-870,🔀 ONE-870 - Create new auto validating textfield component and add it to customer workplace form (pull request #678),Components,2024-10-15T18:01:28+00:00,2024-10-15T18:01:28+00:00,1,603,44,647,559,22,1,direct_commit,107.5,1,Rodrigo Mejia,6
ONE-1097,🔀 ONE-1097 - Update String Copies for long capture (pull request #676),Onboarding,2024-10-11T15:02:54+00:00,2024-10-11T15:02:54+00:00,1,69,12,81,57,4,1,direct_commit,16.5,1,Julio Rico,6
ONE-1081,🔀 ONE-1081 -  Add missing biometry validation on the transfer to lifemiles process (pull request #677),MenuCard,2024-10-10T17:52:02+00:00,2024-10-10T17:52:02+00:00,1,71,24,95,47,3,1,direct_commit,15.3,1,Edgar Emilio Vásquez Castillo,6
ONE-1082,🔀 ONE-1082 - Update String Copies for short capture (pull request #675),Menu,2024-10-08T22:12:36+00:00,2024-10-08T22:12:36+00:00,1,66,15,81,51,16,1,direct_commit,40.35,1,Julio Rico,6
ONE-909,✨ ONE-909 - Add Localizations to Demographic Section (pull request #674),Onboarding,2024-10-08T18:11:31+00:00,2024-10-08T18:11:31+00:00,1,343,28,371,315,28,1,direct_commit,92.7,1,Julio Rico,6
ONE-988,🔀 ONE-988 - Change voice intonation for short capture and creates a new string catalog  (pull request #663),Swift Code,2024-10-07T15:11:03+00:00,2024-10-07T15:11:03+00:00,1,815,57,872,758,56,1,direct_commit,197.35,1,Vladimir Guevara,6
ONEAPP-1483,🔀 ONEAPP-1483 - Cherry-pick ticket ONEAPP-1468 from the develop version to 1.13.0. (pull request #670),Menu,2024-09-30T19:04:27+00:00,2024-09-30T19:04:27+00:00,1,44,46,90,-2,2,1,direct_commit,11.7,1,Emely Melgar,6
ONEAPP-1468,🔀 ONEAPP-1483 - Cherry-pick ticket ONEAPP-1468 from the develop version to 1.13.0. (pull request #670),Menu,2024-09-26T22:59:46+00:00,2024-09-30T19:04:27+00:00,3,88,92,180,-4,2,3,direct_commit,20.4,1,Emely Melgar,18
ONEAPP-1448,🔀 ONEAPP-1448 - Add spinner for card options change (pull request #668),MenuCard,2024-09-27T18:30:02+00:00,2024-09-27T18:30:02+00:00,1,22,9,31,13,2,1,direct_commit,7.65,1,Rodrigo Mejia,6
ONEAPP-1479,🔀 ONEAPP-1479 - Fix text and padding (pull request #666),Main,2024-09-26T20:57:32+00:00,2024-09-26T20:57:32+00:00,1,4,2,6,2,2,1,direct_commit,5.5,1,Julio Rico,6
ONEAPP-1465,🔀 ONEAPP-1465 - Add correct alert logic to Card Payment method view (pull request #665),MenuCard,2024-09-25T17:00:28+00:00,2024-09-25T17:00:28+00:00,1,33,8,41,25,1,1,direct_commit,6.7,1,Rodrigo Mejia,6
ONEAPP-1210,🔀 ONEAPP-1210 - Update Texts on Offer Acceptance (pull request #664),Onboarding,2024-09-24T22:59:30+00:00,2024-09-24T22:59:30+00:00,1,8,8,16,0,7,1,direct_commit,16.2,1,Julio Rico,6
ONEAPP-193,🔀 ONEAPP-193 - Onboarding - Educational Institution (pull request #657),Swift Code,2024-09-24T15:57:45+00:00,2024-09-24T15:57:45+00:00,1,3829,374,4203,3455,31,1,direct_commit,464.6,1,Julio Rico,6
ONEAPP-1301,🔀 ONEAPP-1301 - Fix visual errors in card cancellation flow (pull request #661),Home,2024-09-23T16:58:06+00:00,2024-09-23T16:58:06+00:00,1,31,24,55,7,6,1,direct_commit,17.3,1,Rodrigo Mejia,6
ONEAPP-1420,🔀 ONEAPP-1420 - Use ProfileSuccessView for password resert or user recovery,Navigation,2024-09-20T17:09:30+00:00,2024-09-23T14:55:48+00:00,2,33,14,47,19,2,2,direct_commit,10.0,1,Vladimir Guevara,12
ONEAPP-1322,🔀 ONEAPP-1322 - Move the user to the welcome page. (pull request #660),Main,2024-09-19T15:59:32+00:00,2024-09-20T19:18:29+00:00,1,76,24,100,52,8,2,direct_commit,26.8,1,Emely Melgar,6
ONEAPP-1436,🔀 ONEAPP-1436 - Corrects Payment With Points success view,Menu,2024-09-18T21:38:52+00:00,2024-09-18T21:38:52+00:00,1,99,88,187,11,19,1,direct_commit,53.3,1,Vladimir Guevara,6
ONEAPP-1437,🔀 ONEAPP-1437 - Redirect to selfie capture instead pop when user close the tutorial. (pull request #650),Identity,2024-09-17T18:52:36+00:00,2024-09-17T18:52:36+00:00,1,10,10,20,0,2,1,direct_commit,6.5,1,Emely Melgar,6
ONEAPP-1428,🔀 ONEAPP-1428 - Add missing padding in ticket view button (pull request #649),MenuCard,2024-09-16T15:58:14+00:00,2024-09-17T17:08:10+00:00,1,64,16,80,48,10,3,direct_commit,30.2,1,Rodrigo Mejia,6
ONEAPP-1433,🔀  ONEAPP-1433_ONEAPP-1191 - Credit card empty state cherrypick (pull request #647),DesignSystem,2024-09-17T15:36:02+00:00,2024-09-17T15:36:02+00:00,1,300,0,300,300,14,1,direct_commit,59.0,1,Emely Melgar,6
ONEAPP-1191,🔀  ONEAPP-1433_ONEAPP-1191 - Credit card empty state cherrypick (pull request #647),DesignSystem,2024-09-12T17:36:34+00:00,2024-09-17T15:36:02+00:00,4,600,0,600,600,14,2,direct_commit,90.0,2,Emely Melgar; Julio Rico,48
ONEAPP-1430,🔀 ONEAPP-1430_ONEAPP-1431 - Improvement update Selfie and Document wording. (pull request #646),Identity,2024-09-16T18:41:33+00:00,2024-09-16T18:41:33+00:00,1,42,16,58,26,12,1,direct_commit,30.0,1,Emely Melgar,6
ONEAPP-1431,🔀 ONEAPP-1430_ONEAPP-1431 - Improvement update Selfie and Document wording. (pull request #646),Identity,2024-09-16T18:41:33+00:00,2024-09-16T18:41:33+00:00,1,42,16,58,26,12,1,direct_commit,30.0,1,Emely Melgar,6
ONEAPP-883,🔀 ONEAPP-883 - Payment With Points Optimization,Main,2024-09-16T15:58:07+00:00,2024-09-16T15:58:07+00:00,1,900,694,1594,206,40,1,direct_commit,205.7,1,Vladimir Guevara,6
ONEAPP-1155,🔀 ONEAPP-1155_ONEAPP-1175 - Include Document and Selfie Tutorial for Onboarding Process. (pull request #640),{Main => Identity},2024-09-13T15:37:41+00:00,2024-09-13T15:37:41+00:00,1,1296,73,1369,1223,78,1,direct_commit,290.25,1,Emely Melgar,6
ONEAPP-1175,🔀 ONEAPP-1155_ONEAPP-1175 - Include Document and Selfie Tutorial for Onboarding Process. (pull request #640),{Main => Identity},2024-09-13T15:37:41+00:00,2024-09-13T15:37:41+00:00,1,1296,73,1369,1223,78,1,direct_commit,290.25,1,Emely Melgar,6
ONEAPP-192,🔀 ONEAPP-192 - Onboarding - Education Level (pull request #639),Swift Code,2024-09-13T02:48:41+00:00,2024-09-13T02:48:41+00:00,1,660,166,826,494,32,1,direct_commit,139.3,1,Julio Rico,6
ONEAPP-1296,🔀 ONEAPP-1296 - Refactor card payment flow (pull request #641),Swift Code,2024-09-12T22:02:02+00:00,2024-09-12T22:02:02+00:00,1,2413,1620,4033,793,67,1,direct_commit,457.3,1,Rodrigo Mejia,6
ONE-1,⚗️ ONE-1 - Jira automation Testing. (pull request #637),Home,2024-09-05T20:57:17+00:00,2024-09-05T20:57:17+00:00,1,20,20,40,0,1,1,direct_commit,6.0,1,Emely Melgar,6
ONEAPP-1340,🔀 ONEAPP-1340 - Fix some card replacement flow issues. (pull request #636),Core,2024-09-05T14:56:36+00:00,2024-09-05T14:56:36+00:00,1,38,39,77,-1,9,1,direct_commit,24.75,1,Emely Melgar,6
ONEAPP-1337,ONEAPP-1337 - CcApplicationst3 incorrect data (pull request #635),Swift Code,2024-09-04T16:19:39+00:00,2024-09-04T16:19:39+00:00,1,10,10,20,0,4,1,direct_commit,10.5,1,Julio Rico,6
ONEAPP-40,:twisted_rightwards_arrows: ONEAPP-40 - Onboarding optimization - Dependents,Swift Code,2024-08-30T22:07:18+00:00,2024-08-30T22:07:18+00:00,1,1087,391,1478,696,40,1,direct_commit,209.25,1,Julio Rico,6
ONEAPP-1261,🔀 ONEAPP-1261 - Adds PhoneNumber Validation in ReferenceContactView,Onboarding,2024-08-29T15:24:32+00:00,2024-08-29T15:24:32+00:00,1,243,30,273,213,7,1,direct_commit,40.8,1,Vladimir Guevara,6
ONEAPP-1316,🔀 ONEAPP-1316 - Fix password reset flow (pull request #632),PasswordReset,2024-08-28T16:37:20+00:00,2024-08-28T16:37:20+00:00,1,278,22,300,256,10,1,direct_commit,49.9,1,Rodrigo Mejia,6
ONEAPP-1303,🔀 ONEAPP-1303 - Add phone number validation in Familiar and Personal References,Swift Code,2024-08-22T16:48:37+00:00,2024-08-22T16:48:37+00:00,1,271,130,401,141,6,1,direct_commit,46.6,1,Vladimir Guevara,6
ONEAPP-1284,🔀 ONEAPP-1284 - Return custom error with server message in password change flow (pull request #625),Components,2024-08-21T23:43:15+00:00,2024-08-21T23:43:15+00:00,1,110,68,178,42,9,1,direct_commit,33.4,1,Rodrigo Mejia,6
ONEAPP-1279,🔀 ONEAPP-1279 - Fix login horizontal padding. (pull request #626),Main,2024-08-21T17:17:37+00:00,2024-08-21T17:17:37+00:00,1,2,2,4,0,2,1,direct_commit,5.3,1,Emely Melgar,6
ONEAPP-1283,🐛ONEAPP-1283 - Merged in bugfix/ONEAPP-1283 (pull request #622),Onboarding,2024-08-19T13:12:05+00:00,2024-08-19T13:12:05+00:00,1,34,36,70,-2,4,2,direct_commit,15.2,1,Julio Rico,6
ONEAPP-967,🔀 ONEAPP-967 - Add new alert for invalid credentials error (pull request #620),Main,2024-08-16T21:42:32+00:00,2024-08-16T21:42:32+00:00,1,28,4,32,24,3,1,direct_commit,10.0,1,Rodrigo Mejia,6
ONEAPP-1267,🔀 ONEAPP-1267 - Update CredoLab dependencies using CocoaPods and upgrade to the latest version. (pull request #624),Swift Code,2024-08-16T21:41:30+00:00,2024-08-16T21:41:30+00:00,1,48,12331,12379,-12283,159,1,direct_commit,940.35,1,Emely Melgar,6
ONEAPP-1268,🔀 ONEAPP-1268 - Fix Unrecognized Transaction UI Defects (pull request #623),Main,2024-08-16T20:52:13+00:00,2024-08-16T20:52:13+00:00,1,27,11,38,16,7,1,direct_commit,18.25,1,Josseh Blanco,6
ONEAPP-1068,🔀 ONEAPP-1068 - Don't allow phone numbers previously registered for company information in onboarding,Components,2024-08-15T16:07:08+00:00,2024-08-15T16:07:08+00:00,1,255,30,285,225,11,1,direct_commit,50.0,1,Vladimir Guevara,6
ONEAPP-37,🔀 ONEAPP-37 - Onboarding optimization - Marital Status,Swift Code,2024-08-15T13:55:28+00:00,2024-08-15T13:55:28+00:00,1,745,18,763,727,18,1,direct_commit,112.4,1,Julio Rico,6
ONEAPP-639,🔀 ONEAPP-639 - Enhance OTP Channel Selection and Streamline Reset Password Flow (pull request #595),Components,2024-08-14T23:11:53+00:00,2024-08-14T23:11:53+00:00,1,500,282,782,218,19,1,direct_commit,103.1,1,Gabriel Campos,6
ONEAPP-632,🔀 ONEAPP-632 - PART I: Remove companies request from splash view. (pull request #603),Splash,2024-08-13T16:59:30+00:00,2024-08-13T16:59:30+00:00,1,1,164,165,-163,1,1,direct_commit,11.3,1,Emely Melgar,6
ONEAPP-1259,🔀 ONEAPP-1259 - Fixes lack of coordination between slider and text field at CreditCardLimitSliderView,Components,2024-08-13T15:38:18+00:00,2024-08-13T15:38:18+00:00,1,7,1,8,6,1,1,direct_commit,3.75,1,Vladimir Guevara,6
ONEAPP-1239,🔀 ONEAPP-1239/1231 - Fix multiple regression UI issues (pull request #599),Main,2024-08-12T15:45:42+00:00,2024-08-12T15:45:42+00:00,1,4,2,6,2,3,1,direct_commit,7.5,1,Josseh Blanco,6
ONEAPP-1188,🔀 ONEAPP-1188 - Fix UI in income review. (pull request #598),Onboarding,2024-08-12T15:44:40+00:00,2024-08-12T15:44:40+00:00,1,7,7,14,0,1,1,direct_commit,4.05,1,Gabriel Campos,6
ONEAPP-1189,🔀 ONEAPP-1189 - Fix valid phone numbers being marked as invalid in the customer workplace information view (pull request #597),Onboarding,2024-08-12T15:42:44+00:00,2024-08-12T15:42:44+00:00,1,1,3,4,-2,2,1,direct_commit,5.25,1,Josseh Blanco,6
ONEAPP-1249,🔀 ONEAPP-1249 - Add the brand authenticity footer to the guest and login views (pull request #596),Components,2024-08-09T20:08:07+00:00,2024-08-09T20:08:07+00:00,1,168,44,212,124,8,1,direct_commit,36.0,1,Josseh Blanco,6
ONEAPP-1168,🔀 ONEAPP-1168 - Add the new Document and Selfie Coordinators for the short capture flow (pull request #591),Main,2024-08-08T22:14:49+00:00,2024-08-08T22:14:49+00:00,1,3144,650,3794,2494,60,1,direct_commit,467.9,1,Josseh Blanco,6
ONEAPP-845,🔀 ONEAPP-845 - Refactor beneficiaries percentage validation. (pull request #594),Onboarding,2024-08-01T16:33:56+00:00,2024-08-08T22:11:50+00:00,7,103,22,125,81,8,2,direct_commit,29.4,1,Gabriel Campos,42
ONEAPP-200,🔀 ONEAPP-200 - Set Up Familiy References Module,Swift Code,2024-08-02T18:17:31+00:00,2024-08-02T18:17:31+00:00,1,1020,2056,3076,-1036,40,1,direct_commit,285.8,1,Vladimir Guevara,6
ONEAPP-1215,🔀 ONEAPP-1215 - Fix UI issues in LifeMiles points transfer view (pull request #587),MenuCard,2024-08-01T16:53:16+00:00,2024-08-01T16:53:16+00:00,1,8,4,12,4,2,1,direct_commit,6.0,1,Gabriel Campos,6
ONEAPP-652,🔀 💬 ONEAPP-652 - Change formatter of currency (pull request #581),Swift Code,2024-07-31T20:55:33+00:00,2024-07-31T20:55:33+00:00,1,125,89,214,36,31,1,direct_commit,79.95,1,Edgar Emilio Vásquez Castillo,6
ONEAPP-1225,Merged in bugfix/ONEAPP-1225 (pull request #582),Components,2024-07-31T16:24:15+00:00,2024-07-31T16:24:15+00:00,1,66,39,105,27,5,1,direct_commit,19.55,1,Vladimir Guevara,6
ONEAPP-1206,🔀 ONEAPP-1206 - Fix invisible markers in the Map Snapshot View (pull request #580),Main,2024-07-30T15:36:31+00:00,2024-07-30T15:36:31+00:00,1,8,1,9,7,1,1,direct_commit,3.85,1,Josseh Blanco,6
ONEAPP-1181,🔀 ONEAPP-1181 - Add new phone number Regex (pull request #578),Core,2024-07-26T15:54:57+00:00,2024-07-26T15:54:57+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Rodrigo Mejia,6
ONEAPP-1197,🔀 ONEAPP-1197 - Presents CreditCardLimitSlider when limits are not nil,AdditionalCard,2024-07-26T15:47:05+00:00,2024-07-26T15:47:05+00:00,1,24,32,56,-8,3,1,direct_commit,11.0,1,Vladimir Guevara,6
ONEAPP-1008,🔀 ONEAPP-1008 - Update Facephi ID and Selfie frameworks version. (pull request #572),Other,2024-07-25T15:08:31+00:00,2024-07-25T15:08:31+00:00,1,6302,11437,17739,-5135,1031,1,direct_commit,3265.05,1,Emely Melgar,6
ONEAPP-1179,🔀 ONEAPP-1179 - Fix address selection details view padding (pull request #573),Main,2024-07-24T04:12:39+00:00,2024-07-24T04:12:39+00:00,1,8,251,259,-243,5,1,direct_commit,24.35,1,Josseh Blanco,6
ONEAPP-882,🔀 ONEAPP-882 - Optimize transfer from card points to lifemiles flow (pull request #571),Swift Code,2024-07-23T22:02:02+00:00,2024-07-23T22:02:02+00:00,1,2024,1636,3660,388,52,1,direct_commit,389.2,1,Rodrigo Mejia,6
ONEAPP-1177,🔀 ONEAPP-1177 - Fix miles title alert. (pull request #570),Onboarding,2024-07-22T22:58:11+00:00,2024-07-22T22:58:11+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Edgar Emilio Vásquez Castillo,6
ONEAPP-963,🔀 ONEAPP-963 - Add the new design for the credit card offer resolution screens (pull request #567),Onboarding,2024-07-22T22:56:16+00:00,2024-07-22T22:56:16+00:00,1,1624,1877,3501,-253,52,1,direct_commit,361.25,1,Josseh Blanco,6
ONEAPP-875,🔀 ONEAPP-875 - Adds new TextFieldType for credit card limit input,Components,2024-07-22T20:57:00+00:00,2024-07-22T20:57:00+00:00,1,147,68,215,79,9,1,direct_commit,37.1,1,Vladimir Guevara,6
ONEAPP-1174,🔀 ONEAPP-1174 - Limit Google's geocoding results to El Salvador (pull request #569),Onboarding,2024-07-22T20:53:56+00:00,2024-07-22T20:53:56+00:00,1,13,8,21,5,2,1,direct_commit,6.7,1,Josseh Blanco,6
ONEAPP-240,🔀 ONEAPP-240 - Unrecognized transactions (pull request #552),Main,2024-07-19T21:46:18+00:00,2024-07-19T21:46:18+00:00,1,2263,66,2329,2197,57,1,direct_commit,344.6,1,Edgar Emilio Vásquez Castillo,6
ONEAPP-836,🔀 ONEAPP-836 - Change Card Benefits Texts (pull request #558),Components,2024-07-19T21:31:21+00:00,2024-07-19T21:31:21+00:00,1,13,5,18,8,4,1,direct_commit,10.55,1,Edgar Emilio Vásquez Castillo,6
ONEAPP-831,🔀 ONEAPP-831 - Present pop up for credit information authorization (pull request #551),Main,2024-07-19T21:29:22+00:00,2024-07-19T21:29:22+00:00,1,218,9,227,209,6,1,direct_commit,35.25,1,Vladimir Guevara,6
ONEAPP-924,🔀 ONEAPP-924 - Add new map snapshot for all the Address Selection flows (pull request #550),Onboarding,2024-07-19T21:26:03+00:00,2024-07-19T21:26:03+00:00,1,1218,498,1716,720,49,1,direct_commit,245.7,1,Josseh Blanco,6
ONEAPP-1167,🔀 ONEAPP-1167 - Fix the refresh token alert's button text (pull request #566),Components,2024-07-19T18:25:33+00:00,2024-07-19T18:25:33+00:00,1,6,2,8,4,1,1,direct_commit,3.7,1,Josseh Blanco,6
SP-1804,Merged in support/bugfix/ONEAPP-SP-1804 (pull request #565),Home,2024-07-19T18:00:36+00:00,2024-07-19T18:00:36+00:00,1,1,0,1,1,1,1,direct_commit,3.1,1,José De la O,6
ONEAPP-1065,🔀 ONEAPP-1065 - Add new AFP Verification screen to the optimized flow (pull request #564),Onboarding,2024-07-18T23:21:38+00:00,2024-07-18T23:21:38+00:00,1,43,97,140,-54,8,1,direct_commit,26.15,1,Josseh Blanco,6
ONEAPP-1082,🔀 ONEAPP-1082 - Replace life miles fixed string with realm values. (pull request #559),Swift Code,2024-07-15T22:51:47+00:00,2024-07-17T19:47:23+00:00,1,85,84,169,1,2,2,direct_commit,18.7,1,Emely Melgar,6
ONEAPP-956,🔀 ONEAPP-956 - Update the Megapoints filtering logic to support search by period. (pull request #553),Core,2024-07-16T16:27:35+00:00,2024-07-16T16:27:35+00:00,1,62,13,75,49,4,1,direct_commit,15.85,1,Emely Melgar,6
ONEAPP-1055,🔀 ONEAPP-1055: Fixed issue on DigitalSignRadioButton that prevents to be tapable on all empty spaces.  (pull request #556),Main,2024-07-12T15:28:20+00:00,2024-07-12T15:28:20+00:00,1,1,0,1,1,1,1,direct_commit,3.1,1,David Cortes,6
ONEAPP-957,🔀 ONEAPP-957 - Add the new AFPVerification screen (pull request #554),Onboarding,2024-07-11T22:57:19+00:00,2024-07-11T22:57:19+00:00,1,304,84,388,220,11,1,direct_commit,57.6,1,Josseh Blanco,6
ONEAPP-820,🔀 ONEAPP-820 (pull request #555),Swift Code,2024-07-11T22:26:28+00:00,2024-07-11T22:26:28+00:00,1,2,1,3,1,1,1,direct_commit,3.25,1,David Cortes,6
ONEAPP-795,🔀 ONEAPP-795 - Adds New RadioButtonStyle exclusive for DeliveryAddresListView,Components,2024-07-09T15:54:54+00:00,2024-07-09T15:54:54+00:00,1,169,8,177,161,8,1,direct_commit,34.3,1,Vladimir Guevara,6
ONEAPP-1006,🔀 ONEAPP-1006 - Set the minimum deployment to iOS 17 (pull request #549),Build System,2024-07-08T22:11:24+00:00,2024-07-08T22:11:24+00:00,1,13,16,29,-3,1,1,direct_commit,5.1,1,Josseh Blanco,6
ONEAPP-953,🔀ONEAPP-953 - Fix in app Amplitude event tracking (pull request #545),Menu,2024-07-05T16:25:25+00:00,2024-07-05T16:25:25+00:00,1,18,1,19,17,2,1,direct_commit,6.85,1,Rodrigo Mejia,6
ONEAPP-943,🔀 ONEAPP-943 - Modify the fastlane match commands in the README.md (pull request #544),Other,2024-07-03T23:32:30+00:00,2024-07-03T23:32:30+00:00,1,2,2,4,0,1,1,direct_commit,3.3,1,Josseh Blanco,6
ONEAPP-934,🔀 ONEAPP-934: Fixed range validation on money textfields (pull request #540),Components,2024-07-03T17:26:29+00:00,2024-07-03T17:26:29+00:00,1,8,2,10,6,2,1,direct_commit,5.9,1,David Cortes,6
ONEAPP-962,🔀 ONEAPP-962 - Update wording for protection plan screens. (pull request #543),Menu,2024-07-02T23:23:10+00:00,2024-07-02T23:23:10+00:00,1,4,4,8,0,1,1,direct_commit,3.6,1,Emely Melgar,6
ONEAPP-954,🔀 ONEAPP-954_ONEAPP-959 - Fix menu option and typo issues. (pull request #542),Main,2024-07-02T23:18:58+00:00,2024-07-02T23:18:58+00:00,1,15,6,21,9,2,1,direct_commit,6.8,1,Emely Melgar,6
ONEAPP-959,🔀 ONEAPP-954_ONEAPP-959 - Fix menu option and typo issues. (pull request #542),Main,2024-07-02T23:18:58+00:00,2024-07-02T23:18:58+00:00,1,15,6,21,9,2,1,direct_commit,6.8,1,Emely Melgar,6
ONEAPP-808,🔀 ONEAPP-808 - Fix navigation error in AFP flow (pull request #541),Onboarding,2024-07-02T23:16:01+00:00,2024-07-02T23:16:01+00:00,1,17,12,29,5,3,1,direct_commit,9.3,1,Rodrigo Mejia,6
ONEAPP-907,🔀 ONEAPP-907 - Fix regex handling in work address form (pull request #535),Core,2024-07-01T23:15:05+00:00,2024-07-01T23:15:05+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Rodrigo Mejia,6
ONEAPP-903,🔀 ONEAPP-903 - Clean Search Text in ListSelectableModifier,Components,2024-07-01T23:14:32+00:00,2024-07-01T23:14:32+00:00,1,1,0,1,1,1,1,direct_commit,3.1,1,Vladimir Guevara,6
ONEAPP-939,🔀 ONEAPP-939 - Remove back button for income form and hide lis divider. (pull request #534),Onboarding,2024-07-01T23:07:05+00:00,2024-07-01T23:07:05+00:00,1,7,3,10,4,3,1,direct_commit,7.85,1,Emely Melgar,6
ONEAPP-895,🔀 ONEAPP-895 - Receive flag to  show next view in TranslatePointsLifeMilesView Bugfix/ONEAPP-895,Swift Code,2024-06-27T17:04:42+00:00,2024-06-28T23:01:06+00:00,1,24,15,39,9,2,3,direct_commit,10.15,1,Vladimir Guevara,6
ONEAPP-838,🔀 ONEAPP-838: Fixed keyboard issues on other amount (pull request #533),Components,2024-06-21T17:17:19+00:00,2024-07-01T17:55:24+00:00,10,59,32,91,27,5,3,direct_commit,20.5,1,David Cortes,60
ONEAPP-948,🔀 ONEAPP-948 - Update business logic to present amount alert for payment process. (pull request #532),Swift Code,2024-06-29T01:06:37+00:00,2024-06-29T01:06:37+00:00,1,25,10,35,15,2,1,direct_commit,8.0,1,Emely Melgar,6
ONEAPP-904,🔀 ONEAPP-904 - Replace short document images with raw images for Facephi validation. (pull request #524),Core,2024-06-27T17:20:15+00:00,2024-06-27T17:20:15+00:00,1,115,98,213,17,4,1,direct_commit,25.4,1,Emely Melgar,6
ONEAPP-880,🔀 ONEAPP-880 - Change the card customization name limit to 21 characters. (pull request #523),AdditionalCard,2024-06-27T17:10:01+00:00,2024-06-27T17:10:01+00:00,1,4,4,8,0,3,1,direct_commit,7.6,1,Josseh Blanco,6
ONEAPP-937,🔀 ONEAPP-937 - Fixed issue on phone numbers mask (pull request #527),Onboarding,2024-06-28T19:38:53+00:00,2024-06-28T19:38:53+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,David Cortes,6
ONEAPP-824,"🔀 ONEAPP-824 - Fix deletion request behavior, receiving on the main queue.",Main,2024-06-18T15:25:58+00:00,2024-06-24T17:12:07+00:00,6,82,32,114,50,12,3,direct_commit,36.8,1,Emely Melgar,36
ONEAPP-658,🔀 ONEAPP-658- Add new Amplitude Analytics events (pull request #513),Onboarding,2024-06-21T17:13:39+00:00,2024-06-21T17:13:39+00:00,1,241,13,254,228,22,1,direct_commit,69.75,1,Rodrigo Mejia,6
ONEAPP-810,🔀 ONEAPP-810 - Add other options for Companies catalogs search (pull request #514),Components,2024-06-20T23:58:14+00:00,2024-06-20T23:58:14+00:00,1,79,45,124,34,7,1,direct_commit,25.15,1,Edgar Emilio Vásquez Castillo,6
SP-1598,🔀 ONEAPP-SP-1598 - Showing popup alert when amountAvailable is empty or equals to zero (pull request #509),Swift Code,2024-06-19T02:45:31+00:00,2024-06-19T02:45:31+00:00,1,10,0,10,10,1,1,direct_commit,4.0,1,Emely Melgar,6
ONEAPP-828,🔀 ONEAPP-828 - Disabled movements and account state options when mainCard isn't active. (pull request #511),Home,2024-06-19T20:24:47+00:00,2024-06-19T20:24:47+00:00,1,13,7,20,6,3,1,direct_commit,8.65,1,Emely Melgar,6
ONEAPP-648,🔀 ONEAPP-648 - Deleted card selector (pull request #507),Swift Code,2024-06-18T22:29:07+00:00,2024-06-18T22:29:07+00:00,1,87,79,166,8,2,1,direct_commit,17.65,1,David Cortes,6
ONEAPP-815,🔀 ONEAPP-815 - Add date validation on PEP form (pull request #504),Onboarding,2024-06-18T17:32:04+00:00,2024-06-18T17:32:04+00:00,1,135,172,307,-37,8,1,direct_commit,39.1,1,Edgar Emilio Vásquez Castillo,6
ONEAPP-615,🔀 ONEAPP-615 - Include account deletion feature. (pull request #496),Main,2024-06-14T16:26:07+00:00,2024-06-14T16:26:07+00:00,1,3146,737,3883,2409,75,1,direct_commit,502.45,1,Emely Melgar,6
ONEAPP-198,🎨 ONEAPP-198 - Customer Income Flow Refactor (pull request #503),Onboarding,2024-06-13T20:59:20+00:00,2024-06-13T20:59:20+00:00,1,3587,514,4101,3073,76,1,direct_commit,537.4,1,Edgar Emilio Vásquez Castillo,6
ONEAPP-659,🔀 ONEAPP-659 - Add mixed incomes alert and not salaried rejection (pull request #502),Swift Code,2024-06-13T17:50:05+00:00,2024-06-13T17:50:05+00:00,1,1061,1264,2325,-203,29,1,direct_commit,228.3,1,Rodrigo Mejia,6
ONEAPP-644,🔀 ONEAPP-644 - Fix tap bug on some views (pull request #501),Swift Code,2024-06-13T17:17:35+00:00,2024-06-13T17:17:35+00:00,1,27,29,56,-2,5,1,direct_commit,15.15,1,David Cortes,6
ONEAPP-640,🔀 ONEAPP-640 - Add the new Menu Alerts UI and implementation (pull request #499),Components,2024-06-12T15:27:20+00:00,2024-06-12T15:27:20+00:00,1,1276,500,1776,776,34,1,direct_commit,221.6,1,Josseh Blanco,6
ONEAPP-746,🔀 ONEAPP-746 - Hide the card limit slider if the card is an additional type. (pull request #498),Swift Code,2024-06-10T23:26:23+00:00,2024-06-10T23:26:23+00:00,1,25,5,30,20,2,1,direct_commit,7.75,1,Emely Melgar,6
ONEAPP-799,🔀 ONEAPP-799 - Fix bug years number PEPCondition (pull request #497),Onboarding,2024-06-10T21:57:47+00:00,2024-06-10T21:57:47+00:00,1,35,3,38,32,2,1,direct_commit,8.65,1,David Cortes,6
ONEAPP-735,🔀 ONEAPP-735 - Fix bug on press x button on alert  (pull request #494),Main,2024-06-07T16:56:55+00:00,2024-06-07T16:56:55+00:00,1,2,1,3,1,1,1,direct_commit,3.25,1,David Cortes,6
ONEAPP-783,🔀 ONEAPP-783 - Fix pickers defects in PEPConditionForm (pull request #491),Onboarding,2024-06-05T15:41:31+00:00,2024-06-05T15:41:31+00:00,1,4,2,6,2,1,1,direct_commit,3.5,1,Vladimir Guevara,6
ONEAPP-782,🔀 ONEAPP-782- Fixes errors when fetch catalogs in PEP bonding  (pull request #490),Onboarding,2024-06-05T15:40:19+00:00,2024-06-05T15:40:19+00:00,1,15,7,22,8,2,1,direct_commit,6.85,1,Vladimir Guevara,6
ONEAPP-651,🔀 ONEAPP-651 - Adds new alert case for enable card limit,Swift Code,2024-06-03T23:28:57+00:00,2024-06-03T23:28:57+00:00,1,87,50,137,37,7,1,direct_commit,26.2,1,Vladimir Guevara,6
ONEAPP-785,🔀 ONEAPP-785 - Redirect the user to document scanning when a document error occurs. (pull request #488),Onboarding,2024-06-03T16:51:12+00:00,2024-06-03T16:51:12+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Emely Melgar,6
ONEAPP-714,🔀 ONEAPP-714 - Set PEP Condition navigation module,Onboarding,2024-05-30T21:44:01+00:00,2024-05-30T21:44:01+00:00,1,1810,1499,3309,311,38,1,direct_commit,332.95,1,Vladimir Guevara,6
ONEAPP-617,🔀 ONEAPP-617 - Add the new centralized map and map form component (pull request #485),Swift Code,2024-05-30T20:36:34+00:00,2024-05-30T20:36:34+00:00,1,2152,7641,9793,-5489,106,1,direct_commit,810.25,1,Josseh Blanco,6
ONEAPP-755,🔀 ONEAPP-755 - Remove back button while displaying loader in AFP flow (pull request #483),Onboarding,2024-05-27T17:56:52+00:00,2024-05-28T23:15:35+00:00,1,2,0,2,2,2,2,direct_commit,6.2,1,Rodrigo Mejia,6
ONEAPP-199,🔀 ONEAPP-199 - Refactor AFP onboarding flow (pull request #477),Onboarding,2024-05-24T00:35:21+00:00,2024-05-24T00:35:21+00:00,1,880,653,1533,227,29,1,direct_commit,179.65,1,Rodrigo Mejia,6
ONEAPP-729,🔀 ONEAPP-729 - Include padding for address cell. (pull request #480),Swift Code,2024-05-24T00:16:17+00:00,2024-05-24T00:16:17+00:00,1,2,1,3,1,1,1,direct_commit,3.25,1,Emely Melgar,6
ONEAPP-745,🔀 ONEAPP-745_746_748 - Include multiple fixes for My Card Options feature.  (pull request #481),Swift Code,2024-05-24T00:14:15+00:00,2024-05-24T00:14:15+00:00,1,74,50,124,24,4,1,direct_commit,18.9,1,Emely Melgar,6
ONEAPP-504,🔀 ONEAPP-504 - Picker Implementation (pull request #479),Components,2024-05-22T21:00:00+00:00,2024-05-22T21:00:00+00:00,1,422,160,582,262,13,1,direct_commit,77.2,1,Edgar Emilio Vásquez Castillo,6
ONEAPP-715,🔀 ONEAPP-715 - Remove commented terms and conditions menu option. (pull request #476),Menu,2024-05-22T15:48:07+00:00,2024-05-22T15:48:07+00:00,1,3,3,6,0,1,1,direct_commit,3.45,1,Emely Melgar,6
ONEAPP-281,🔀 ONEAPP-281 - Include Mega Points detail screen. (pull request #475),Main,2024-05-17T17:25:24+00:00,2024-05-17T17:25:24+00:00,1,1139,22,1161,1117,18,1,direct_commit,152.0,1,Emely Melgar,6
ONEAPP-712,🔀 ONEAPP-712 - Fix invalid date format in OCR document validation (pull request #474),Menu,2024-05-14T18:17:30+00:00,2024-05-14T18:17:30+00:00,1,21,2,23,19,3,1,direct_commit,9.2,1,Rodrigo Mejia,6
ONEAPP-702,🔀 ONEAPP-702 - Add navigation on tap and fix banner title (pull request #472),Core,2024-05-13T22:43:41+00:00,2024-05-13T22:43:41+00:00,1,30,33,63,-3,4,1,direct_commit,13.65,1,Rodrigo Mejia,6
ONEAPP-608,🔀 ONEAPP-608 - Add validations to prevent users from saving a short capture address with empty coordinates (pull request #473),Main,2024-05-13T22:31:50+00:00,2024-05-13T22:31:50+00:00,1,522,295,817,227,16,1,direct_commit,99.95,1,Josseh Blanco,6
ONEAPP-638,🔀 ONEAPP-638 - repair visual details in card activation (pull request #471),Core,2024-05-13T21:30:49+00:00,2024-05-13T21:30:49+00:00,1,124,22,146,102,7,1,direct_commit,28.5,1,Vladimir Guevara,6
ONEAPP-622,💄 ONEAPP-622 - Add masking to the phone number (pull request #470),Home,2024-05-13T20:38:12+00:00,2024-05-13T20:38:12+00:00,1,41,9,50,32,6,1,direct_commit,17.55,1,Edgar Emilio Vásquez Castillo,6
ONEAPP-587,🔀 ONEAPP-587 - Fix UI visual defects in Datos generales (DUI Capture flow) (pull request #469),Menu,2024-05-08T21:20:00+00:00,2024-05-09T20:18:26+00:00,1,77,58,135,19,8,2,direct_commit,28.6,1,Rodrigo Mejia,6
ONEAPP-623,🔀 ONEAPP-623 - Improves otp validation in contact Info flow,Menu,2024-05-09T20:05:54+00:00,2024-05-09T20:05:54+00:00,1,35,32,67,3,1,1,direct_commit,8.1,1,Vladimir Guevara,6
ONEAPP-627,"🔀 ONEAPP-627 - Fix labels typos, date formatter and encoding error. (pull request #467)",Swift Code,2024-05-09T00:46:40+00:00,2024-05-09T00:46:40+00:00,1,4,5,9,-1,3,1,direct_commit,7.65,1,Emely Melgar,6
ONEAPP-494,🔀 ONEAPP-494 - Add available label title for cashback(pull request #466),Home,2024-05-08T21:48:04+00:00,2024-05-08T21:48:04+00:00,1,21,12,33,9,10,1,direct_commit,23.7,1,Edgar Emilio Vásquez Castillo,6
ONEAPP-279,🔀 ONEAPP-279 - Improve MegaPoints card and add interaction (pull request #468),Swift Code,2024-05-08T20:48:42+00:00,2024-05-08T20:48:42+00:00,1,503,216,719,287,17,1,direct_commit,96.1,1,Rodrigo Mejia,6
ONEAPP-588,🔀 ONEAPP-588 - Fix starting working date label (pull request #457),Menu,2024-05-07T21:46:37+00:00,2024-05-07T21:46:37+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Josseh Blanco,6
ONEAPP-624,🔀 ONEAPP-624 - Move binidngs setup to ProfileContactInfoViewModel,Menu,2024-05-07T21:33:33+00:00,2024-05-07T21:33:33+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Vladimir Guevara,6
ONEAPP-469,🔀 ONEAPP-469 - Handle empty account statement,DesignSystem,2024-05-07T21:32:38+00:00,2024-05-07T21:32:38+00:00,1,46,3,49,43,4,1,direct_commit,13.75,1,Vladimir Guevara,6
ONEAPP-611,🔀 ONEAPP-611 - Add Facebook SDK Initialization (pull request #456),Swift Code,2024-04-29T17:35:55+00:00,2024-04-30T17:12:22+00:00,1,174,6,180,168,9,2,direct_commit,37.7,2,Edgar Emilio Vásquez Castillo; Rodrigo Mejia,12
ONEAPP-612,🔀 ONEAPP-612  - Add AppSealing missing policy (pull request #454),Other,2024-04-29T23:44:22+00:00,2024-04-29T23:44:22+00:00,1,8,0,8,8,1,1,direct_commit,3.8,1,Edgar Emilio Vásquez Castillo,6
ONEAPP-499,Merged in bugfix/ONEAPP-499 (pull request #455),Components,2024-04-30T16:23:32+00:00,2024-04-30T16:23:32+00:00,1,12,5,17,7,5,1,direct_commit,12.45,1,Vladimir Guevara,6
ONEAPP-586,🔀 ONEAPP-586 - Update biometryProccessID persistence. (pull request #448),Core,2024-04-23T20:15:53+00:00,2024-04-24T18:31:20+00:00,1,536,158,694,378,40,2,direct_commit,143.5,1,Emely Melgar,6
ONEAPP-600,🔀 🐛 ONEAPP-600 - Include close actions for benefits alerts. (pull request #446),Menu,2024-04-24T16:05:31+00:00,2024-04-24T16:05:31+00:00,1,218,216,434,2,3,1,direct_commit,39.6,1,Emely Melgar,6
ONEAPP-595,🔀 ONEAPP-595 - Integrate Singular SDK (pull request #444),Swift Code,2024-04-24T15:48:40+00:00,2024-04-24T15:48:40+00:00,1,151,9,160,142,7,1,direct_commit,30.55,1,Edgar Emilio Vásquez Castillo,6
ONEAPP-563,🔀 ONEAPP-563 - Fix stale onboarding session alert (pull request #445),Main,2024-04-22T22:24:31+00:00,2024-04-23T23:29:02+00:00,1,16,2,18,14,4,2,direct_commit,11.7,2,Vladimir Guevara; Josseh Blanco,12
ONEAPP-564,🔀 ONEAPP-564 - Fix duplicated /GeoZones call (pull request #430),Components,2024-04-22T16:35:38+00:00,2024-04-22T16:35:38+00:00,1,60,34,94,26,3,1,direct_commit,14.7,1,Josseh Blanco,6
ONEAPP-589,🔀 ONEAPP-589 - Show correct error message in password reset screen (pull request #429),Core,2024-04-19T19:07:21+00:00,2024-04-19T19:07:21+00:00,1,365,230,595,135,8,1,direct_commit,65.0,1,Rodrigo Mejia,6
ONEAPP-480,🔀 ONEAPP-480 - Corrects Card details component dimensions,Swift Code,2024-04-19T17:03:33+00:00,2024-04-19T17:03:33+00:00,1,2,1,3,1,1,1,direct_commit,3.25,1,Vladimir Guevara,6
ONEAPP-508,🔧 ONEAPP-508 - Appstore Review (pull request #427),Other,2024-04-17T17:54:00+00:00,2024-04-17T17:54:00+00:00,1,679,13388,14067,-12709,584,1,direct_commit,1906.3,1,Edgar Emilio Vásquez Castillo,6
ONEAPP-592,💚 ONEAPP-592 - Change upload dsym just after build creation (pull request #437),Other,2024-04-22T18:39:53+00:00,2024-04-22T20:33:55+00:00,1,99,33,132,66,3,2,direct_commit,19.55,1,Edgar Emilio Vásquez Castillo,6
ONEAPP-248,🔀 ONEAPP-248 - fetch card information in CardActivationDigitsInteracto,Main,2024-04-16T18:08:21+00:00,2024-04-16T18:08:21+00:00,1,110,13,123,97,6,1,direct_commit,24.65,1,Vladimir Guevara,6
ONEAPP-565,🔀ONEAPP-565 - Remove extra call in CompanyCatalogs (pull request #421),Splash,2024-04-12T21:02:06+00:00,2024-04-12T21:02:06+00:00,1,11,5,16,6,1,1,direct_commit,4.35,1,Rodrigo Mejia,6
ONEAPP-540,🔀 ONEAPP-540 - Remove the twilio chat option from the customer support options (pull request #419),Navigation,2024-04-11T22:47:54+00:00,2024-04-11T22:47:54+00:00,1,21,26,47,-5,3,1,direct_commit,10.4,1,Josseh Blanco,6
ONEAPP-541,🔀 ONEAPP-541 - Pop to root view when income type is not validated,Swift Code,2024-04-11T20:50:50+00:00,2024-04-11T20:50:50+00:00,1,20,14,34,6,1,1,direct_commit,5.7,1,Vladimir Guevara,6
ONEAPP-544,🔀ONEAPP-544 - Modifies global popup alert action to redirect user to root,Swift Code,2024-04-10T23:16:28+00:00,2024-04-10T23:16:28+00:00,1,20,7,27,13,2,1,direct_commit,7.35,1,Vladimir Guevara,6
ONEAPP-553,🔀 ONEAPP-553 - Fix dependencies provider error for catalogs. (pull request #414),Splash,2024-04-10T05:21:59+00:00,2024-04-10T05:21:59+00:00,1,8,1,9,7,2,1,direct_commit,5.85,1,Emely Melgar,6
ONEAPP-502,🔀 ONEAPP-502 - Add new maintenance period view (pull request #412),Swift Code,2024-04-08T02:43:23+00:00,2024-04-09T23:04:22+00:00,1,835,499,1334,336,25,3,direct_commit,161.45,1,Rodrigo Mejia,6
ONEAPP-536,🔀 ONEAPP-536 - Fix some UI defect for onboarding (pull request #411),Onboarding,2024-04-08T16:42:37+00:00,2024-04-09T18:48:29+00:00,1,35,40,75,-5,3,2,direct_commit,13.5,1,Emely Melgar,6
ONEAPP-535,🔀 ONEAPP-535_546_536_544 - Include remote flag to show custom alert or server error and multiple fixes. (pull request #410),Main,2024-04-09T02:50:06+00:00,2024-04-09T02:50:06+00:00,1,333,634,967,-301,62,1,direct_commit,190.0,1,Emely Melgar,6
ONEAPP-500,"🔀 ONEAPP-500 - Fix session alert being persisted after closing the app without tapping ""accept"" (pull request #408)",Core,2024-04-08T06:34:41+00:00,2024-04-08T20:07:22+00:00,1,1073,96,1169,977,34,2,direct_commit,182.1,1,Josseh Blanco,6
ONEAPP-510,🔀 ONEAPP-510 - Monitoring Tool for Production (pull request #405),Swift Code,2024-04-08T04:20:35+00:00,2024-04-08T04:20:35+00:00,1,446,53,499,393,31,1,direct_commit,110.25,1,Edgar Emilio Vásquez Castillo,6
ONEAPP-506,🔀  ONEAPP-506 - Show user friendly message when receiving a 500 error (pull request #404),Swift Code,2024-04-08T03:48:56+00:00,2024-04-08T03:48:56+00:00,1,40,24,64,16,1,1,direct_commit,8.2,1,Vladimir Guevara,6
ONEAPP-498,🔀 ONEAPP-498_ONEAPP-275 - Part 2: Include business logic to handler morphology timer. (pull request #400),Main,2024-04-04T20:00:33+00:00,2024-04-06T00:29:24+00:00,1,650,388,1038,262,27,2,direct_commit,140.4,1,Emely Melgar,6
ONEAPP-275,🔀 ONEAPP-498_ONEAPP-275 - Part 2: Include business logic to handler morphology timer. (pull request #400),Main,2024-03-21T00:00:45+00:00,2024-04-06T00:29:24+00:00,16,861,673,1534,188,27,3,direct_commit,176.75,1,Emely Melgar,96
ONEAPP-260,🔀 ONEAPP-260 - Add the working contact editing option in my profile (pull request #368),Menu,2024-03-20T20:37:25+00:00,2024-04-01T21:44:25+00:00,12,1655,455,2110,1200,37,2,direct_commit,264.25,1,Josseh Blanco,72
ONEAPP-258,🔀 ONEAPP-258 - Add Personal details modification flow (pull request #373),Menu,2024-03-27T19:57:37+00:00,2024-03-27T19:57:37+00:00,1,2027,62,2089,1965,36,1,direct_commit,278.8,1,Rodrigo Mejia,6
ONEAPP-262,🔀 ONEAPP-262 - Creates CustomerContactsView and remove methods on ProfileContactInfoView,Menu,2024-03-27T16:34:41+00:00,2024-03-27T16:34:41+00:00,1,1476,96,1572,1380,31,1,direct_commit,215.4,1,Vladimir Guevara,6
ONEAPP-280,🔀 ONEAPP-280 - Cashback Transactions (pull request #370),Swift Code,2024-03-27T16:23:16+00:00,2024-03-27T16:23:16+00:00,1,733,326,1059,407,22,1,direct_commit,134.6,1,Edgar Emilio Vásquez Castillo,6
ONEAPP-428,🔀 ONEAPP-428_450_347 - Multiples fixes for OCR process and ccapplications request (pull request #391),Onboarding,2024-03-26T19:32:31+00:00,2024-03-26T19:32:31+00:00,1,222,249,471,-27,17,1,direct_commit,69.65,1,Emely Melgar,6
ONEAPP-319,🔀 ONEAPP-319 - Add support for unknown status codes and the blocked user status code in CCApplications (pull request #389),Core,2024-03-26T14:34:15+00:00,2024-03-26T14:34:15+00:00,1,84,70,154,14,2,1,direct_commit,16.9,1,Josseh Blanco,6
ONEAPP-467,🔀 ONEAPP-467 - Validate OneAppAlertData code (pull request #393),Core,2024-03-26T14:33:17+00:00,2024-03-26T14:33:17+00:00,1,15,1,16,14,2,1,direct_commit,6.55,1,Vladimir Guevara,6
ONEAPP-446,🔀 ONEAPP-446-Fix AFP Flow (pull request #392),Swift Code,2024-03-25T18:14:42+00:00,2024-03-25T18:14:42+00:00,1,42,99,141,-57,9,1,direct_commit,28.15,1,Edgar Emilio Vásquez Castillo,6
ONEAPP-460,🔀 ONEAPP-460 - Fix card activation flow (pull request #386),Main,2024-03-25T00:26:52+00:00,2024-03-25T00:26:52+00:00,1,264,148,412,116,9,1,direct_commit,52.8,1,Rodrigo Mejia,6
ONEAPP-447,🔀 ONEAPP-447 - Handles network errors in income details view (pull request #387),Swift Code,2024-03-25T00:26:07+00:00,2024-03-25T00:26:07+00:00,1,36,42,78,-6,4,1,direct_commit,14.7,1,Vladimir Guevara,6
ONEAPP-443,🔀 ONEAPP-443 - Remove guar let for protection plan contract.  (pull request #379),Core,2024-03-24T17:22:03+00:00,2024-03-24T17:22:03+00:00,1,13,15,28,-2,6,1,direct_commit,15.05,1,Emely Melgar,6
ONEAPP-233,🔀 ONEAPP-233 - Fix Account Statement flow (pull request #378),Core,2024-03-24T17:11:37+00:00,2024-03-24T17:11:37+00:00,1,749,328,1077,421,20,1,direct_commit,132.3,1,Rodrigo Mejia,6
ONEAPP-462,🔀 ONEAPP-462 - Show an alert every time the token expires (pull request #382),Main,2024-03-24T17:02:50+00:00,2024-03-24T17:02:50+00:00,1,104,5,109,99,13,1,direct_commit,37.65,1,Josseh Blanco,6
ONEAPP-32,🔀 ONEAPP-32 - Adds new OneTextField component for LoginPage credentials (pull request #383),Core,2024-01-23T19:12:43+00:00,2024-03-24T17:01:58+00:00,60,953,466,1419,487,49,4,direct_commit,220.6,3,Vladimir Guevara; Josseh Blanco; Emely Melgar,1080
ONEAPP-438,🔀 ONEAPP-438 Remove local error for EndOnboardingCheckpoint. (pull request #376),Main,2024-03-24T16:54:42+00:00,2024-03-24T16:54:42+00:00,1,70,149,219,-79,10,1,direct_commit,35.45,1,Emely Melgar,6
ONEAPP-439,🔀 ONEAPP-439 - Make the continue button sticky in the SelfieScanView (pull request #381),Components,2024-03-24T15:02:27+00:00,2024-03-24T15:02:27+00:00,1,79,47,126,32,5,1,direct_commit,21.25,1,Josseh Blanco,6
ONEAPP-449,🔀 ONEAPP-449 - Fix checkpoint routing for the CCANextStep parameters (pull request #377),Main,2024-03-24T03:13:39+00:00,2024-03-24T03:13:39+00:00,1,2,4,6,-2,1,1,direct_commit,3.4,1,Josseh Blanco,6
ONEAPP-291,🔀 ONEAPP-291 - Optimize the Quick Login View (pull request #375),Core,2024-03-08T20:22:08+00:00,2024-03-23T17:17:30+00:00,14,1475,1082,2557,393,38,2,direct_commit,279.6,1,Josseh Blanco,84
ONEAPP-448,🔀  bugfix/ONEAPP-448 (pull request #374),Onboarding,2024-03-23T03:26:13+00:00,2024-03-23T03:26:13+00:00,1,9,3,12,6,4,1,direct_commit,10.05,1,Edgar Emilio Vásquez Castillo,6
ONEAPP-432,🔀 ONEAPP-432 - Fix some ui issues. (pull request #372),Menu,2024-03-21T22:39:32+00:00,2024-03-21T22:39:32+00:00,1,22,28,50,-6,5,1,direct_commit,14.6,1,Emely Melgar,6
ONEAPP-437,🔀 ONEAPP-437 - Defects (pull request #371),Menu,2024-03-21T21:53:05+00:00,2024-03-21T21:53:05+00:00,1,5,4,9,1,3,1,direct_commit,7.7,1,Edgar Emilio Vásquez Castillo,6
ONEAPP-255,"🔀 ONEAPP-255 - Part 3: Include add, update and delete profile address. (pull request #367)",Menu,2024-03-06T16:36:17+00:00,2024-03-21T01:27:21+00:00,14,3114,178,3292,2936,49,4,direct_commit,422.3,2,Edgar Emilio Vásquez Castillo; Emely Melgar,168
ONEAPP-264,🔀 ONEAPP-264 - References Personal Data (pull request #363),Menu,2024-03-20T17:57:52+00:00,2024-03-20T17:57:52+00:00,1,2250,1317,3567,933,57,1,direct_commit,405.85,1,Edgar Emilio Vásquez Castillo,6
SYNC-1,🔀 Merged in sync-1.10.0-to-develop (pull request #355),Core,2024-03-11T12:25:41-06:00,2024-03-13T00:06:56+00:00,1,660,602,1262,58,25,6,direct_commit,152.1,1,Emely Melgar,6
ONEAPP-341,🔀 ONEAPP-341- Add Default case to the CCStatus. (pull request #348),Core,2024-03-12T16:35:47+00:00,2024-03-12T16:35:47+00:00,1,26,5,31,21,3,1,direct_commit,9.85,1,Edgar Emilio Vásquez Castillo,6
ONEAPP-337,🔀 ONEAPP-337 - Send the onboarding parameters has optionals.  (pull request #344),Core,2024-03-09T00:09:09+00:00,2024-03-09T00:09:09+00:00,1,138,127,265,11,18,1,direct_commit,57.15,1,Emely Melgar,6
ONEAPP-336,🔀 ONEAPP-336 - Remove the back button for contract screen. (pull request #343),Main,2024-03-08T20:35:42+00:00,2024-03-08T20:35:42+00:00,1,3,0,3,3,2,1,direct_commit,5.3,1,Emely Melgar,6
ONEAPP-314,🔀 ONEAPP-314 - Implement Card Replacement flow (pull request #338),MenuCard,2024-03-08T18:21:17+00:00,2024-03-08T18:21:17+00:00,1,1082,38,1120,1044,35,1,direct_commit,181.1,1,Edgar Emilio Vásquez Castillo,6
ONEAPP-331,🔀 ONEAPP-331 - Fix UI components in Card Documents flow (pull request #340),Menu,2024-03-08T15:11:27+00:00,2024-03-08T15:11:27+00:00,1,16,32,48,-16,5,1,direct_commit,14.2,1,Rodrigo Mejia,6
ONEAPP-328,🔀 🎨 ONEAPP-328 - Fix defects (pull request #337),Onboarding,2024-03-07T23:50:19+00:00,2024-03-07T23:50:19+00:00,1,2,1,3,1,2,1,direct_commit,5.25,1,Edgar Emilio Vásquez Castillo,6
ONEAPP-272,🔀 ONEAPP-272 - Add new cashback account UI (pull request #336),Swift Code,2024-03-07T22:04:46+00:00,2024-03-07T22:04:46+00:00,1,295,255,550,40,19,1,direct_commit,81.25,1,Rodrigo Mejia,6
ONEAPP-224,🔀 ONEAPP-224 - Fix label position for reset password form. (pull request #334),Components,2024-03-07T17:08:02+00:00,2024-03-07T17:08:02+00:00,1,14,20,34,-6,2,1,direct_commit,7.4,1,Emely Melgar,6
ONEAPP-271,🔀 ONEAPP-271 - Add new Documents and Contracts UI and services. (pull request #333),Menu,2024-03-07T15:17:06+00:00,2024-03-07T15:17:06+00:00,1,1285,631,1916,654,36,1,direct_commit,233.05,1,Rodrigo Mejia,6
ONEAPP-267,🔀 ONEAPP-267 - Modifies complaintDetail route to handle back action,Menu,2024-03-07T14:26:18+00:00,2024-03-07T14:26:18+00:00,1,801,34,835,767,15,1,direct_commit,112.8,1,Vladimir Guevara,6
ONEAPP-244,✨ ONEAPP-244 - Card Block Feature (pull request #331),Swift Code,2024-03-06T19:43:58+00:00,2024-03-06T19:43:58+00:00,1,1187,97,1284,1090,39,1,direct_commit,202.55,1,Edgar Emilio Vásquez Castillo,6
ONEAPP-251,🔀 ONEAPP-251 - Include the feature to re-activate the protection plan. (pull request #326),Menu,2024-03-02T22:01:10+00:00,2024-03-02T22:01:10+00:00,1,1402,2770,4172,-1368,72,1,direct_commit,423.7,1,Emely Melgar,6
ONEAPP-300,🔀 ONEAPP-300 - User URLEncoding to disabled protection plan.  (pull request #318),Onboarding,2024-02-28T05:17:38+00:00,2024-02-28T16:05:30+00:00,1,50,18,68,32,10,2,direct_commit,27.9,1,Emely Melgar,6
ONEAPP-298,🔀 ONEAPP-298 - Change Icon on Complaints Submission high priority,Menu,2024-02-27T17:02:51+00:00,2024-02-27T17:02:51+00:00,1,2,1,3,1,1,1,direct_commit,3.25,1,Vladimir Guevara,6
ONEAPP-171,🔀 ONEAPP-171 - Include optimization process for protection plan. (pull request #314),{Main,2024-02-27T00:33:00+00:00,2024-02-27T00:33:00+00:00,1,1686,813,2499,873,48,1,direct_commit,306.25,1,Emely Melgar,6
ONEAPP-170,🔀 ONEAPP-170 - Implements Complaints Attention flow (pull request #311),Swift Code,2024-02-26T23:35:32+00:00,2024-02-26T23:35:32+00:00,1,1360,789,2149,571,45,1,direct_commit,266.45,1,Vladimir Guevara,6
ONEAPP-90,🔀 ONEAPP-90 - Refactor the Onboarding survey (pull request #313),Survey,2024-02-26T22:27:01+00:00,2024-02-26T22:27:01+00:00,1,1798,1740,3538,58,50,1,direct_commit,367.8,1,Josseh Blanco,6
ONEAPP-139,🔀 ONEAPP-139 - Add new trusted devices UI and Logic. (pull request #310),Swift Code,2024-02-23T21:13:57+00:00,2024-02-23T21:13:57+00:00,1,856,307,1163,549,28,1,direct_commit,157.95,1,Rodrigo Mejia,6
ONEAPP-89,🔀 ONEAPP-89 - Refactor onboarding customization card process.  (pull request #305),Swift Code,2024-02-23T17:24:55+00:00,2024-02-23T17:24:55+00:00,1,1371,1986,3357,-615,126,1,direct_commit,489.4,1,Emely Melgar,6
ONEAPP-98,🔀 ONEAPP-98 - Improve card transactions pagination and layout (pull request #307),Main,2024-02-22T23:58:11+00:00,2024-02-22T23:58:11+00:00,1,100,29,129,71,7,1,direct_commit,26.45,1,Rodrigo Mejia,6
ONEAPP-88,🔀 ONEAPP-88 - Delivery Address (pull request #308),Main,2024-02-22T20:26:02+00:00,2024-02-22T20:26:02+00:00,1,1938,1369,3307,569,55,1,direct_commit,373.25,1,Edgar Emilio Vásquez Castillo,6
ONEAPP-286,🔀 ONEAPP-286_ONEAPP-284 - Fix some ui defect in the onboarding flow.  (pull request #309),Main,2024-02-22T16:56:27+00:00,2024-02-22T16:56:27+00:00,1,120,123,243,-3,13,1,direct_commit,45.15,1,Emely Melgar,6
ONEAPP-284,🔀 ONEAPP-286_ONEAPP-284 - Fix some ui defect in the onboarding flow.  (pull request #309),Main,2024-02-22T16:56:27+00:00,2024-02-22T16:56:27+00:00,1,120,123,243,-3,13,1,direct_commit,45.15,1,Emely Melgar,6
ONEAPP-96,🔀 ONEAPP-96 - Add new transaction cards UI. (pull request #304),Main,2024-02-20T22:51:43+00:00,2024-02-20T22:51:43+00:00,1,406,75,481,331,15,1,direct_commit,75.35,1,Rodrigo Mejia,6
ONEAPP-236,🔀 ONEAPP-236 - Bug fixing (pull request #306),Onboarding,2024-02-20T22:46:45+00:00,2024-02-20T22:46:45+00:00,1,35,20,55,15,5,1,direct_commit,15.5,1,Edgar Emilio Vásquez Castillo,6
ONEAPP-87,🔀 ONEAPP-87 - Refactor contract signature process. (pull request #297),Core,2024-02-20T01:27:44+00:00,2024-02-20T01:27:44+00:00,1,1516,832,2348,684,47,1,direct_commit,288.2,1,Emely Melgar,6
ONEAPP-131,🔀 ONEAPP-131 - Adds new card status label and masked card number in cards cancellation,Swift Code,2024-02-19T15:25:31+00:00,2024-02-19T15:25:31+00:00,1,51,33,84,18,6,1,direct_commit,19.75,1,Vladimir Guevara,6
ONEAPP-86,🔀 ONEAPP-86 - Benefits and Saving Account (pull request #298),Swift Code,2024-02-13T00:33:10+00:00,2024-02-13T00:33:10+00:00,1,2579,2524,5103,55,87,1,direct_commit,559.1,1,Edgar Emilio Vásquez Castillo,6
ONEAPP-178,🔀 ONEAPP-178 - Prevents color change on dark mode. (pull request #302),Main,2024-02-10T00:53:16+00:00,2024-02-12T17:17:35+00:00,2,137,37,174,100,7,3,direct_commit,32.55,1,Rodrigo Mejia,12
ONEAPP-100,🔀 ONEAPP-100 - Activity Logs Implementation (pull request #296),Swift Code,2024-02-09T23:29:01+00:00,2024-02-09T23:29:01+00:00,1,937,403,1340,534,23,1,direct_commit,160.85,1,Vladimir Guevara,6
ONEAPP-16,🔀 ONEAPP-16 - Refactor card transactions screen filters (pull request #294),Swift Code,2024-02-09T17:12:40+00:00,2024-02-09T17:12:40+00:00,1,1820,727,2547,1093,53,1,direct_commit,325.35,1,Rodrigo Mejia,6
ONEAPP-164,🔀 ONEAPP-164 - Include shouldShowAllOptions flag to show active cards. (pull request #293),Swift Code,2024-02-02T23:59:54+00:00,2024-02-05T21:30:20+00:00,2,91,32,123,59,13,2,direct_commit,38.7,1,Emely Melgar,12
ONEAPP-148,🔀 ONEAPP-148_ONEAPP-153 - Refactor close actions logic to the thirdDeliveryAttempt alert. (pull request #291),Core,2024-01-31T18:18:22+00:00,2024-01-31T18:18:22+00:00,1,42,33,75,9,6,1,direct_commit,18.85,1,Emely Melgar,6
ONEAPP-153,🔀 ONEAPP-148_ONEAPP-153 - Refactor close actions logic to the thirdDeliveryAttempt alert. (pull request #291),Core,2024-01-31T18:18:22+00:00,2024-01-31T18:18:22+00:00,1,42,33,75,9,6,1,direct_commit,18.85,1,Emely Melgar,6
ONEAPP-103,🔀  ONEAPP-103 - Send Correct amplitude event attribute (pull request #290),Core,2024-01-30T00:22:14+00:00,2024-01-30T00:22:14+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Vladimir Guevara,6
ONEAPP-8,🔀 ONEAPP-8 - Add the third delivery attempt flow (pull request #288),Main,2024-01-29T14:58:06+00:00,2024-01-29T14:58:06+00:00,1,1064,276,1340,788,59,1,direct_commit,239.2,1,Josseh Blanco,6
ONEAPP-59,🔀 ONEAPP-59 - Third attempt to deliver additional card (Failed case) (pull request #289),Components,2024-01-28T21:48:01+00:00,2024-01-28T21:48:01+00:00,1,196,56,252,140,14,1,direct_commit,51.4,1,Vladimir Guevara,6
ONEAPP-67,🔀 ONEAPP-67 - OneApp Alerts Enhancement (pull request #285),Swift Code,2024-01-27T00:27:18+00:00,2024-01-27T00:27:18+00:00,1,472,193,665,279,22,1,direct_commit,101.85,1,Vladimir Guevara,6
ONEAPP-128,🔀 ONEAPP-128 - Final additional card hotfixes. (pull request #281),Main,2024-01-25T07:14:42+00:00,2024-01-26T01:04:17+00:00,1,81,50,131,31,20,2,direct_commit,52.6,1,Emely Melgar,6
ONEAPP-10,🔀 ONEAPP-10 - Change card activation success button text (pull request #280),Swift Code,2024-01-24T01:25:22+00:00,2024-01-25T23:31:35+00:00,1,539,329,868,210,22,2,direct_commit,116.35,1,Rodrigo Mejia,6
ONEAPP-123,🔀 ONEAPP-123 - Removes progress bar in card activation flow (pull request #276),Swift Code,2024-01-25T05:38:03+00:00,2024-01-25T16:40:20+00:00,1,449,129,578,320,31,2,direct_commit,115.35,1,Rodrigo Mejia,6
ONEAPP-127,🔀 ONEAPP-127 - Fix card color picker behavior.  (pull request #275),Components,2024-01-25T14:48:22+00:00,2024-01-25T14:48:22+00:00,1,14,10,24,4,3,1,direct_commit,8.9,1,Emely Melgar,6
ONEAPP-124,"🔀 ONEAPP-123, ONEAPP-124 & ONEAPP-125 - Fixes UI defects (pull request #272)",Swift Code,2024-01-25T05:38:03+00:00,2024-01-25T05:38:03+00:00,1,447,127,574,320,31,1,direct_commit,114.05,1,Rodrigo Mejia,6
ONEAPP-125,"🔀 ONEAPP-123, ONEAPP-124 & ONEAPP-125 - Fixes UI defects (pull request #272)",Swift Code,2024-01-25T05:38:03+00:00,2024-01-25T05:38:03+00:00,1,447,127,574,320,31,1,direct_commit,114.05,1,Rodrigo Mejia,6
ONEAPP-126,🔀 ONEAPP-126 - Resolve bug with the selection on the picker (pull request #270),Components,2024-01-24T12:31:25-06:00,2024-01-24T19:57:58+00:00,1,8,0,8,8,1,2,direct_commit,4.8,2,Emilio Vasquez; Edgar Emilio Vásquez Castillo,12
ONEAPP-119,🔀 ONEAPP-119 - Fix credit card layout on card tracking (pull request #268),Menu,2024-01-24T07:05:02+00:00,2024-01-24T07:05:02+00:00,1,177,332,509,-155,4,1,direct_commit,43.3,1,Vladimir Guevara,6
ONEAPP-54,🔀  ONEAPP-54 - Handle new response status code for not delivered card,Swift Code,2024-01-24T06:27:12+00:00,2024-01-24T06:27:12+00:00,1,389,256,645,133,5,1,direct_commit,62.7,1,Vladimir Guevara,6
ONEAPP-28,🔀 ONEAPP-28 - Map and Address Form in Additional Card Flow (pull request #263),Components,2024-01-24T03:06:23+00:00,2024-01-24T03:06:23+00:00,1,1033,782,1815,251,16,1,direct_commit,175.4,1,Edgar Emilio Vásquez Castillo,6
ONEAPP-26,🔀 ONEAPP-26 - Refactor the additional card delivery options flow (pull request #261),Swift Code,2024-01-23T23:47:52+00:00,2024-01-23T23:47:52+00:00,1,1873,1072,2945,801,79,1,direct_commit,399.9,1,Josseh Blanco,6
ONEAPP-33,🔀 ONEAPP-33 - Fixes and improves Card Cancellation flow (pull request #255),Core,2024-01-23T22:39:10+00:00,2024-01-23T22:39:10+00:00,1,2384,909,3293,1475,50,1,direct_commit,384.85,1,Rodrigo Mejia,6
ONEAPP-30,🔀 ONEAPP-30 - Include additional card customization. (pull request #259),Core,2024-01-23T06:26:33+00:00,2024-01-23T06:26:33+00:00,1,1486,217,1703,1269,40,1,direct_commit,240.45,1,Emely Melgar,6
ONEAPP-12,🔀 ONEAPP-12 - Additional card tracking (pull request #258),Other,2024-01-23T02:25:05+00:00,2024-01-23T02:25:05+00:00,1,3197,548,3745,2649,48,1,direct_commit,444.1,1,Vladimir Guevara,6
ONEAPP-5,🔀 ONEAPP-5 - Refactor the additional cardholder contact information view (pull request #254),Components,2024-01-18T23:32:54+00:00,2024-01-18T23:32:54+00:00,1,731,212,943,519,36,1,direct_commit,156.7,1,Josseh Blanco,6
ONEAPP-6,🔀 ONEAPP-6 - Formats expiration date in CardOptionsViewModel (pull request #257),Swift Code,2024-01-17T15:03:18+00:00,2024-01-17T23:38:13+00:00,1,1138,691,1829,447,28,2,direct_commit,206.35,1,Vladimir Guevara,6
ONEAPP-107,🔁 ONEAPP-107 - Apply some UI changes on card menu (pull request #256),Swift Code,2024-01-17T22:16:50+00:00,2024-01-17T22:16:50+00:00,1,5,61,66,-56,3,1,direct_commit,10.55,1,Edgar Emilio Vásquez Castillo,6
ONEAPP-14,🔁 ONEAPP-14 - Improve ChangePin module (pull request #252),Swift Code,2024-01-16T23:59:31+00:00,2024-01-16T23:59:31+00:00,1,1347,1791,3138,-444,53,1,direct_commit,331.25,1,Edgar Emilio Vásquez Castillo,6
ONEAPP-80,🔀 ONEAPP-80 - Fix alert dismiss bug when asking for camera permissions (pull request #251),AdditionalCard,2024-01-09T18:44:16+00:00,2024-01-09T18:44:16+00:00,1,6,3,9,3,3,1,direct_commit,7.75,1,Josseh Blanco,6
ONEAPP-45,Merged in feature/ONEAPP-45 (pull request #247),Components,2024-01-05T14:37:17-06:00,2024-01-09T15:21:27+00:00,3,5725,2368,8093,3357,115,14,direct_commit,934.9,2,Emilio Vasquez; Edgar Emilio Vásquez Castillo,36
ONEAPP-4,🔀 ONEAPP-4 - Fix the Additional Cardholder's Document Scan Flow (pull request #246),Navigation,2024-01-08T16:26:45+00:00,2024-01-08T16:26:45+00:00,1,765,960,1725,-195,37,1,direct_commit,199.5,1,Josseh Blanco,6
ONEAPP-76,🔀 ONEAPP-76 - Hides just MenuAlerts Carrousel when they are empty,Swift Code,2024-01-05T21:13:31+00:00,2024-01-05T21:13:31+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Vladimir Guevara,6
ONEAPP-65,🔀 ONEAPP-65 - Fix credit card payment amount formatting,Swift Code,2024-01-05T20:02:37+00:00,2024-01-05T20:02:37+00:00,1,128,89,217,39,6,1,direct_commit,30.25,1,Vladimir Guevara,6
ONEAPP-22,🔀 ONEAPP-22 - Fix the Additional Cardholder Document Scan View (pull request #243),Components,2024-01-05T17:03:39+00:00,2024-01-05T17:03:39+00:00,1,761,382,1143,379,21,1,direct_commit,138.2,1,Josseh Blanco,6
ONEAPP-20,"🔀 ONEAPP-20 - Refactor Cards Page, removes unnecesary properties and methods in CardsViewModel",Core,2024-01-05T00:44:49+00:00,2024-01-05T00:44:49+00:00,1,843,297,1140,546,22,1,direct_commit,144.15,1,Vladimir Guevara,6
ONEAPP-44,🔀 ONEAPP-44 - Native Pickers Generic Implementation (pull request #237),Components,2024-01-02T23:23:37+00:00,2024-01-02T23:23:37+00:00,1,304,0,304,304,4,1,direct_commit,39.4,1,Edgar Emilio Vásquez Castillo,6
TLNTD-1324,🔀 TLNTD-1324 - Fix bug with the excluded area outside from SV (pull request #234),Components,2023-12-20T22:08:39+00:00,2023-12-20T22:08:39+00:00,1,20,12,32,8,5,1,direct_commit,13.6,1,Edgar Emilio Vásquez Castillo,6
TLNTD-1321,🔀 TLNTD-1321 - Fix logic not sending municipalityId and house number (pull request #233),Swift Code,2023-12-19T23:26:02+00:00,2023-12-19T23:26:02+00:00,1,29,28,57,1,1,1,direct_commit,7.3,1,Rodrigo Mejia,6
TLNTD-1284,"🎨 TLNTD-1284, TLNTD1285 - Refactor Maps View and AddressConfirmation (pull request #231)",Swift Code,2023-12-19T23:12:10+00:00,2023-12-19T23:12:10+00:00,1,1192,1032,2224,160,43,1,direct_commit,257.8,1,Edgar Emilio Vásquez Castillo,6
TLNTD-1282,🔀 TLNTD-1282 - Use new checkpoint implementation in identity validation step  (pull request #230),Navigation,2023-12-19T17:22:05+00:00,2023-12-19T17:22:05+00:00,1,157,143,300,14,5,1,direct_commit,33.85,1,Rodrigo Mejia,6
TLNTD-1276,🔀 TLNTD-1276 - Persist the deviceID across installations to avoid exceeding the device limit (pull request #225),Core,2023-12-18T18:17:31+00:00,2023-12-18T18:17:31+00:00,1,24,9,33,15,3,1,direct_commit,9.85,1,Vladimir Guevara,6
TLNTD-1288,🔀 TLNTD-1288 - Replace image with loading animated gif (pull request #224),Main,2023-12-18T18:16:00+00:00,2023-12-18T18:16:00+00:00,1,35,3,38,32,5,1,direct_commit,14.65,1,Rodrigo Mejia,6
TLNTD-1286,🔀 TLNTD-1286 - makes UI Modifications according to fixe ondoarding design,Onboarding,2023-12-14T20:30:11+00:00,2023-12-14T20:30:11+00:00,1,32,31,63,1,3,1,direct_commit,11.75,1,Vladimir Guevara,6
TLNTD-1244,🔀 TLNTD-1244 - Makes API calls in a reactive way,Swift Code,2023-12-14T17:22:47+00:00,2023-12-14T17:22:47+00:00,1,564,378,942,186,21,1,direct_commit,118.3,1,Vladimir Guevara,6
TLNTD-1283,🔀 TLNTD-1283 - Verify my AddressDUI Improvement (pull request #219),DesignSystem,2023-12-14T15:22:41+00:00,2023-12-14T15:22:41+00:00,1,160,74,234,86,14,1,direct_commit,48.7,1,Edgar Emilio Vásquez Castillo,6
TLNTD-1242,"🔀 TLNTD-1242 - Realm I, Include new implementation to store user information.  (pull request #216)",Swift Code,2023-12-14T14:28:04+00:00,2023-12-14T14:28:04+00:00,1,235,7185,7420,-6950,139,1,direct_commit,661.75,1,Emely Melgar,6
TLNTD-1302,🔀 TLNTD-1302 - Fix receipt image generation (pull request #222),Swift Code,2023-12-12T21:22:25+00:00,2023-12-13T22:10:25+00:00,1,1114,408,1522,706,24,3,direct_commit,182.8,1,Rodrigo Mejia,6
TLNTD-1238,🔀 TLNTD-1238- Fix reposition flow for additional cards (pull request #213),Swift Code,2023-12-07T22:44:58+00:00,2023-12-07T22:44:58+00:00,1,141,42,183,99,11,1,direct_commit,39.2,1,Rodrigo Mejia,6
TLNTD-1236,🔀 TLNTD-1236 - Fixes blocked main card reposition flow (pull request #212),Swift Code,2023-12-07T15:00:26+00:00,2023-12-07T15:00:26+00:00,1,1114,716,1830,398,29,1,direct_commit,206.2,1,Rodrigo Mejia,6
TLNTD-1250,🔀 TLNTD-1250 - Removes Date type insatnce and Passes value date for current transaction,Swift Code,2023-12-06T21:04:19+00:00,2023-12-06T21:04:19+00:00,1,115,106,221,9,4,1,direct_commit,25.8,1,Vladimir Guevara,6
TLNTD-1240,🔀 TLNTD-1240 - Locks operations for users with blocked main card,Swift Code,2023-12-06T21:03:23+00:00,2023-12-06T21:03:23+00:00,1,7,4,11,3,2,1,direct_commit,5.9,1,Vladimir Guevara,6
TLNTD-1205,🔀 TLNTD-1205 - Improves Date to String format,Swift Code,2023-11-28T23:35:07+00:00,2023-11-28T23:35:07+00:00,1,50,21,71,29,7,1,direct_commit,21.05,1,Vladimir Guevara,6
TLNTD-1265,🔀 TLNTD-1265 - Fix custom address coordinates properties.,Main,2023-11-27T10:11:59-06:00,2023-11-27T10:11:59-06:00,1,2,2,4,0,1,1,direct_commit,3.3,1,Emely Melgar,6
TLNTD-1225,🔀 TLNTD-1225 - Changes memberId from int to String. (pull request #203),Swift Code,2023-11-20T17:17:42+00:00,2023-11-20T17:17:42+00:00,1,110,131,241,-21,9,1,direct_commit,36.55,1,Rodrigo Mejia,6
TLNTD-1216,🔀 TLNTD-1216 - Remove the IP address call from the adapt method (pull request #197),Swift Code,2023-11-16T05:26:34+00:00,2023-11-16T05:26:34+00:00,1,4,31,35,-27,3,1,direct_commit,8.95,1,Josseh Blanco,6
TLNTD-1211,🔀 TLNTD-1211 - Show all options in the credit card list dropdown (pull request #196),Swift Code,2023-11-15T22:46:25+00:00,2023-11-15T22:46:25+00:00,1,1,0,1,1,1,1,direct_commit,3.1,1,Josseh Blanco,6
TLNTD-1209,🔀 TLNTD-1209 - Add methods to present a global popup to the MainCoordinato (pull request #195),Swift Code,2023-11-15T06:27:14+00:00,2023-11-15T22:35:21+00:00,1,105,80,185,25,9,2,direct_commit,34.5,2,Josseh Blanco; Emely Melgar,12
TLNTD-1198,🔀 TLNTD-1198 - Rollback hidden white screen and add loading icon. (pull request #194),Swift Code,2023-11-15T05:12:03+00:00,2023-11-15T21:30:17+00:00,1,150,122,272,28,2,2,direct_commit,27.1,1,Rodrigo Mejia,6
TLNTD-1177,🔀 TLNTD-1177 - Fix extra argument in call compiler error. (pull request #191),Swift Code,2023-11-15T00:54:52+00:00,2023-11-15T17:00:17+00:00,1,532,446,978,86,3,2,direct_commit,83.5,1,Emely Melgar,6
TLNTD-1192,🔀 TLNTD-1192 - Fix home broken unit test. (pull request #189),Swift Code,2023-11-15T05:17:01+00:00,2023-11-15T07:34:56+00:00,1,194,89,283,105,9,2,direct_commit,43.85,2,Vladimir Guevara; Emely Melgar,12
TLNTD-1193,🔀 TLNTD-1193 - Fix issues with legacy apicall token refresh (pull request #185),Swift Code,2023-11-15T00:52:27+00:00,2023-11-15T00:52:27+00:00,1,597,740,1337,-143,14,1,direct_commit,125.7,1,Josseh Blanco,6
TLNTD-1172,🔀 TLNTD-1172 - Handle new /login endpoint error status code (pull request #183),Core,2023-11-14T20:09:19+00:00,2023-11-14T20:09:19+00:00,1,4,0,4,4,1,1,direct_commit,3.4,1,Vladimir Guevara,6
TLNTD-1169,🔀 TLNTD-1169 - Temporarily hide purchase not recognized option (pull request #178),Swift Code,2023-11-13T17:54:16+00:00,2023-11-13T17:54:16+00:00,1,3,2,5,1,2,1,direct_commit,5.4,1,Rodrigo Mejia,6
TLNTD-1160,🔀 TLNTD-1160 - Add missing button in payment confirmation (pull request #177),Swift Code,2023-11-11T22:25:02+00:00,2023-11-12T21:19:34+00:00,1,63,35,98,28,5,2,direct_commit,20.05,1,Rodrigo Mejia,6
TLNTD-1161,🔀 TLNTD-1161 - Validate excluded addresses (pull request #176),Components,2023-11-12T01:21:00+00:00,2023-11-12T01:21:00+00:00,1,10,8,18,2,5,1,direct_commit,12.4,1,Vladimir Guevara,6
TLNTD-1158,🔀 TLNTD-1158 - Get the MarriedName from OCR to send ccApplicationst1. (pull request #175),Main,2023-11-12T01:19:01+00:00,2023-11-12T01:19:01+00:00,1,5,4,9,1,1,1,direct_commit,3.7,1,Emely Melgar,6
TLNTD-1146,🔀 TLNTD-1146 - Add new transaction points implementation and logic (pull request #173),Swift Code,2023-11-11T15:37:12+00:00,2023-11-11T15:37:12+00:00,1,49,24,73,25,6,1,direct_commit,19.1,1,Rodrigo Mejia,6
TLNTD-1136,🔀 TLNTD-1136 - Shows correct flow in first log in for password change (pull request #172),Main,2023-11-11T02:42:06+00:00,2023-11-11T02:42:06+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Vladimir Guevara,6
TLNTD-1144,🔀 TLNTD-1144 - Only add names if the string is not empty (pull request #171),Swift Code,2023-11-10T17:38:36+00:00,2023-11-11T02:30:34+00:00,1,393,326,719,67,5,2,direct_commit,67.6,1,Josseh Blanco,6
TLNTD-1142,🔀 TLNTD-1142 - Include workaround to restore the OCR response back to the keychain. (pull request #170),Swift Code,2023-11-11T02:26:52+00:00,2023-11-11T02:26:52+00:00,1,100,61,161,39,42,1,direct_commit,98.05,1,Emely Melgar,6
TLNTD-1119,🔀 TLNTD-1119 - Refactor the UI for the activation card success view. (pull request #169),Main,2023-11-10T19:48:52+00:00,2023-11-10T19:48:52+00:00,1,65,1,66,64,4,1,direct_commit,15.55,1,Emely Melgar,6
TLNTD-1138,🔀 TLNTD-1138 - Hide transfer points option for non travel cards (pull request #165),Swift Code,2023-11-10T06:11:01+00:00,2023-11-10T06:11:01+00:00,1,42,17,59,25,4,1,direct_commit,14.05,1,Rodrigo Mejia,6
TLNTD-1134,🔀 TLNTD-1134 - Show the credit card activation alert based on the user alerts (pull request #163),Swift Code,2023-11-10T06:08:54+00:00,2023-11-10T06:08:54+00:00,1,82,65,147,17,5,1,direct_commit,22.45,1,Josseh Blanco,6
TLNTD-1124,🔀 TLNTD-1124 - Include force update and LM crash fix. (pull request #167),Swift Code,2023-11-10T03:45:50+00:00,2023-11-10T03:45:50+00:00,1,293,376,669,-83,9,1,direct_commit,67.1,1,Emely Melgar,6
TLNTD-1139,🔀 TLNTD-1139 - Shows protection plan detail view  (pull request #164),Configuration,2023-11-10T01:33:07+00:00,2023-11-10T01:33:07+00:00,1,157,41,198,116,6,1,direct_commit,30.75,1,Vladimir Guevara,6
TLNTD-1120,🔀 TLNTD-1120 - Removes log functionality for AmplitudeTrackinManager,Swift Code,2023-11-09T02:07:38+00:00,2023-11-09T02:07:38+00:00,1,74,70,144,4,16,1,direct_commit,43.9,1,Vladimir Guevara,6
TLNTD-654,🔀 TLNTD-654 - Add the AppSealing SDK to the project (pull request #152),Other,2023-11-08T07:44:03+00:00,2023-11-08T07:44:03+00:00,1,2042,53,2095,1989,23,1,direct_commit,253.85,1,Josseh Blanco,6
TLNTD-1043,🔀 TLNTD-1043 - Set flag default value to show bottom bar as true,Navigation,2023-11-08T07:35:36+00:00,2023-11-08T07:35:36+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Vladimir Guevara,6
TLNTD-1109,🔀 TLNTD-1109  - Fix infinite loop after payment with points (pull request #158),Swift Code,2023-11-08T06:28:33+00:00,2023-11-08T06:28:33+00:00,1,7,8,15,-1,3,1,direct_commit,8.1,1,Josseh Blanco,6
TLNTD-975,🔀 TLNTD-975 - Refactor credolab request. (pull request #153),Swift Code,2023-11-08T00:33:35+00:00,2023-11-08T00:33:35+00:00,1,82,81,163,1,3,1,direct_commit,19.25,1,Emely Melgar,6
TLNTD-1117,🔀 TLNTD-1117 - Use the OCR Response to fill the available names (pull request #156),Swift Code,2023-11-07T21:54:49+00:00,2023-11-07T21:54:49+00:00,1,30,39,69,-9,2,1,direct_commit,9.95,1,Josseh Blanco,6
TLNTD-1115,🔀 TLNTD-1115 - Include password coordinator in the recovery credential to show support bubble.(pull request #157),Onboarding,2023-11-07T21:27:42+00:00,2023-11-07T21:27:42+00:00,1,46,11,57,35,5,1,direct_commit,16.15,1,Emely Melgar,6
TLNTD-1084,🔀 TLNTD-1084 - Add new error case to biometry auth flow (pull request #151),Core,2023-11-07T04:15:26+00:00,2023-11-07T04:15:26+00:00,1,85,15,100,70,8,1,direct_commit,26.25,1,Rodrigo Mejia,6
TLNTD-968,🔀 TLNTD-968 - Include Recovery User feature and Protection Plan error. (pull request #150),Swift Code,2023-11-07T00:07:47+00:00,2023-11-07T00:07:47+00:00,1,1011,547,1558,464,56,1,direct_commit,241.45,1,Emely Melgar,6
TLNTD-214,🔀 TLNTD-214 - Add Quick Login with FaceID/TouchID (pull request #146),Core,2023-11-06T06:26:28+00:00,2023-11-06T06:26:28+00:00,1,1415,688,2103,727,39,1,direct_commit,254.9,1,Josseh Blanco,6
TLNTD-643,✨ TLNTD-643_TLNTD_273 - Update protection plan process. (pull request #147),Main,2023-11-04T22:25:37+00:00,2023-11-04T22:25:37+00:00,1,1726,994,2720,732,53,1,direct_commit,329.3,1,Emely Melgar,6
TLTND-1007,🔀 TLTND-1007 - Temporarily hide excluded features (pull request #149),Swift Code,2023-11-04T01:00:07+00:00,2023-11-04T01:00:07+00:00,1,481,529,1010,-48,10,1,direct_commit,95.55,1,Rodrigo Mejia,6
TLNTD-978,🔀TLNTD-978 - Adds new flag to display all options when dropdown is active,Swift Code,2023-11-01T15:44:48+00:00,2023-11-01T15:44:48+00:00,1,22,10,32,12,3,1,direct_commit,9.7,1,Vladimir Guevara,6
TLNTD-257,🔀 TLNTD-257 - Makes modification on DateModalView presentation and DateInputView acceptance functionality,Swift Code,2023-10-31T15:29:55+00:00,2023-10-31T15:29:55+00:00,1,192,246,438,-54,8,1,direct_commit,48.5,1,Vladimir Guevara,6
TLNTD-527,🔀 TLNTD-527 - Add insufficient points message in transaction detail view. (pull request #144),Swift Code,2023-10-30T21:24:41+00:00,2023-10-30T21:24:41+00:00,1,93,40,133,53,8,1,direct_commit,28.3,1,Rodrigo Mejia,6
TLNTD-960,🔀 TLNTD-960 - Use bool literals for all AFP-related calls (pull request #142),Core,2023-10-30T00:33:54+00:00,2023-10-30T00:33:54+00:00,1,66,37,103,29,4,1,direct_commit,17.45,1,Josseh Blanco,6
TLNTD-959,🔀 TLNTD-959 - Use 50 quality instead of 95 for document and selfie scans  (pull request #141),Core,2023-10-27T17:41:53+00:00,2023-10-27T17:41:53+00:00,1,30,33,63,-3,7,1,direct_commit,19.65,1,Josseh Blanco,6
TLNTD-940,🔀 TLNTD-940 - Correct message in request for Multimedia permissions  (pull request #139),Main,2023-10-27T16:10:34+00:00,2023-10-27T16:10:34+00:00,1,1,7,8,-6,1,1,direct_commit,3.45,1,Vladimir Guevara,6
TLNTD-526,🔀 TLNTD-526 - Fix options in movements detail view (pull request #138),Swift Code,2023-10-27T15:57:36+00:00,2023-10-27T15:57:36+00:00,1,1167,525,1692,642,26,1,direct_commit,195.95,1,Rodrigo Mejia,6
TLNTD-529,🔀 TLNTD-529 - Fix Navigation when you go back from a transaction (pull request #137),Swift Code,2023-10-26T01:28:05+00:00,2023-10-26T01:28:05+00:00,1,267,215,482,52,4,1,direct_commit,46.45,1,Vladimir Guevara,6
TLNTD-683,"🔀 TLNTD-683 - Include support to Xcode15, iOS 16 and update dependencies. (pull request #136)",Swift Code,2023-10-25T16:56:52+00:00,2023-10-25T16:56:52+00:00,1,172,163,335,9,35,1,direct_commit,96.35,1,Emely Melgar,6
TLNTD-928,🔀 TLNTD-928 - Clean the security tokens each time the app starts (pull request #132),Core,2023-10-17T22:33:47+00:00,2023-10-17T22:33:47+00:00,1,16,2,18,14,3,1,direct_commit,8.7,1,Josseh Blanco,6
TLNTD-915,🔀 TLNTD-915 - Fix some UI issues and OTP code response. (pull request #131),Swift Code,2023-10-17T01:05:15+00:00,2023-10-17T01:05:15+00:00,1,81,57,138,24,11,1,direct_commit,33.95,1,Emely Melgar,6
TLNTD-912,🔀 TLNTD-912 - Include the missing back button for LoginScreen. (pull request #130),Survey,2023-10-16T23:33:03+00:00,2023-10-16T23:33:03+00:00,1,5,4,9,1,3,1,direct_commit,7.7,1,Emely Melgar,6
TLNTD-910,🔀 TLNTD-910 - Multiple onboarding final step bugfixes (pull request #129),Swift Code,2023-10-15T22:20:47+00:00,2023-10-15T22:20:47+00:00,1,61,80,141,-19,3,1,direct_commit,17.1,1,Josseh Blanco,6
TLNTD-895,🔀 TLNTD-895 - Multiple fixes. (pull request #128),Onboarding,2023-10-15T02:30:48+00:00,2023-10-15T07:20:08+00:00,1,38,16,54,22,9,2,direct_commit,24.6,1,Emely Melgar,6
TLNTD-899,🔀 TLNTD-899 - Replace nova button style with new button style for PEP screens.  (pull request #126),Swift Code,2023-10-15T01:44:31+00:00,2023-10-15T01:44:31+00:00,1,55,34,89,21,4,1,direct_commit,16.2,1,Emely Melgar,6
TLNTD-890,🔀 TLNTD-890 - Fix Twilio crash when opening from the password reset flow. (pull request #125),Swift Code,2023-10-14T22:44:25+00:00,2023-10-14T22:44:25+00:00,1,60,16,76,44,10,1,direct_commit,27.8,1,Emely Melgar,6
TLNTD-335,🔀 TLNTD-335 - Add security headers and refresh token logic (pull request #123),Swift Code,2023-10-13T15:12:16+00:00,2023-10-13T15:12:16+00:00,1,7494,2604,10098,4890,391,1,direct_commit,1662.6,1,Josseh Blanco,6
TLNTD-510,🔀 TLNTD-510 & TLNTD-59 - Migrates Twilio to Conversations API (pull request #115),Swift Code,2023-10-12T00:12:29+00:00,2023-10-12T00:12:29+00:00,1,286,909,1195,-623,26,1,direct_commit,127.05,1,Rodrigo Mejia,6
TLNTD-59,🔀 TLNTD-510 & TLNTD-59 - Migrates Twilio to Conversations API (pull request #115),Swift Code,2023-10-12T00:12:29+00:00,2023-10-12T00:12:29+00:00,1,286,909,1195,-623,26,1,direct_commit,127.05,1,Rodrigo Mejia,6
TLNTD-575,🔀 TLNTD-575 - Fix data request for signature protection plan. (pull request #116),Swift Code,2023-10-04T14:08:32+00:00,2023-10-09T16:37:19+00:00,5,550,1015,1565,-465,35,2,direct_commit,177.75,1,Emely Melgar,30
TLNTD-863,🔀 TLNTD-863 - Update gif parameter in signature checkpoint. (pull request #112),Main,2023-10-08T03:18:07+00:00,2023-10-08T21:13:44+00:00,1,7,5,12,2,3,2,direct_commit,8.95,1,Emely Melgar,6
TLNTD-860,🔀 TLNTD-860 - Clean Variable Income (pull request #111),Onboarding,2023-10-08T03:21:29+00:00,2023-10-08T03:21:29+00:00,1,13,0,13,13,1,1,direct_commit,4.3,1,Vladimir Guevara,6
TLNTD-349,🔀 TLNTD-349 - Include face authentication for onboarding signature process.  (pull request #108),Onboarding,2023-10-07T14:49:21+00:00,2023-10-07T14:49:21+00:00,1,686,387,1073,299,27,1,direct_commit,142.95,1,Emely Melgar,6
TLNTD-822,🐛 TLNTD-822_TLNTD-853_TLNTD-855 - Fix the DropDown and SalaryInfoView (pull request #107),Swift Code,2023-10-07T13:03:47+00:00,2023-10-07T13:03:47+00:00,1,30,38,68,-8,5,1,direct_commit,15.9,1,Vladimir Guevara,6
TLNTD-853,🐛 TLNTD-822_TLNTD-853_TLNTD-855 - Fix the DropDown and SalaryInfoView (pull request #107),Swift Code,2023-10-07T13:03:47+00:00,2023-10-07T13:03:47+00:00,1,30,38,68,-8,5,1,direct_commit,15.9,1,Vladimir Guevara,6
TLNTD-855,🐛 TLNTD-822_TLNTD-853_TLNTD-855 - Fix the DropDown and SalaryInfoView (pull request #107),Swift Code,2023-10-07T13:03:47+00:00,2023-10-07T13:03:47+00:00,1,30,38,68,-8,5,1,direct_commit,15.9,1,Vladimir Guevara,6
TLNTD-844,🔀 TLNTD-844 - Hide the face ID/Touch ID toggles temporarily (pull request #105),Swift Code,2023-10-06T22:32:35+00:00,2023-10-06T22:32:35+00:00,1,19,18,37,1,1,1,direct_commit,5.8,1,Josseh Blanco,6
TLNTD-856,🔀 TLNTD-856 - Move the validateOCR request to PersonalInformationViewModel.  (pull request #106),Onboarding,2023-10-06T22:13:46+00:00,2023-10-06T22:13:46+00:00,1,190,313,503,-123,11,1,direct_commit,57.65,1,Emely Melgar,6
TLNTD-356,🔀 TLNTD-356 - Add the OTP and Selfie Scan steps to the password reset flow (pull request #97),Swift Code,2023-10-06T06:21:15+00:00,2023-10-06T06:21:15+00:00,1,1061,691,1752,370,30,1,direct_commit,201.65,1,Josseh Blanco,6
TLNTD-52,🔀 TLNTD-52 - Add Signature Contracts Subscribers modifications (pull request #103),Main,2023-10-06T02:39:19+00:00,2023-10-06T02:39:19+00:00,1,39,23,62,16,2,1,direct_commit,10.05,1,Vladimir Guevara,6
TLNTD-291,🔀 TLNTD-291 - Fixes wrong UI Layouts in PEP flow (pull request #100),Components,2023-10-05T21:18:18+00:00,2023-10-05T21:18:18+00:00,1,131,39,170,92,5,1,direct_commit,26.05,1,Rodrigo Mejia,6
TLNTD-51,🔀 TLNTD-51 - Changes Horizontal padding for PrefferedBenefitView (pull request #102),Swift Code,2023-10-03T16:57:27+00:00,2023-10-05T20:23:25+00:00,2,231,252,483,-21,10,2,direct_commit,57.7,1,Vladimir Guevara,12
TLNTD-66,🔀  TLNTD-66 - Fixes User's salary data persistence (pull request #98),Onboarding,2023-10-05T17:29:48+00:00,2023-10-05T17:29:48+00:00,1,130,134,264,-4,2,1,direct_commit,24.7,1,Vladimir Guevara,6
TLNTD-825,🔀 TLNTD-825 - Avoids refresh municipality when a department  is selected (pull request #99),Swift Code,2023-10-05T17:13:59+00:00,2023-10-05T17:13:59+00:00,1,0,1,1,-1,1,1,direct_commit,3.05,1,Vladimir Guevara,6
TLNTD-317,🔀 TLNTD-317 - Remove the manual signature sketch. (pull request #92),Swift Code,2023-10-03T21:46:40+00:00,2023-10-03T21:46:40+00:00,1,697,502,1199,195,27,1,direct_commit,149.8,1,Emely Melgar,6
TLNTD-544,🔀 TLNTD-544 - Display an error message if the user's contact validation fails  (pull request #90),Onboarding,2023-10-03T21:14:42+00:00,2023-10-03T21:14:42+00:00,1,28,2,30,26,2,1,direct_commit,7.9,1,Josseh Blanco,6
TLNTD-272,🔀 TLNTD-272 - Add new map logic to prevent the user from selecting an invalid delivery address (pull request #89),Onboarding,2023-10-03T18:19:23+00:00,2023-10-03T18:19:23+00:00,1,194,57,251,137,7,1,direct_commit,37.25,1,Josseh Blanco,6
TLNTD-313,🔀 TLNTD-313 - Fixes Card delivery address flow(pull request #94),Swift Code,2023-10-03T17:29:26+00:00,2023-10-03T17:29:26+00:00,1,269,207,476,62,8,1,direct_commit,54.25,1,Rodrigo Mejia,6
TLNTD-47,🔀 TLNTD-47 - Adds a new validation to filter drop down options  (pull request #93),Swift Code,2023-10-03T04:46:38+00:00,2023-10-03T04:46:38+00:00,1,2,1,3,1,1,1,direct_commit,3.25,1,Vladimir Guevara,6
TLNTD-42,🔀 TLNTD-42 - Fix tertiary buttons functionality (pull request #86),Swift Code,2023-10-03T04:41:20+00:00,2023-10-03T04:41:20+00:00,1,182,250,432,-68,10,1,direct_commit,51.7,1,Vladimir Guevara,6
TLNTD-55,🔀 TLNTD-55 - Disables continue button when Satisfaction survey's have not been answered,Swift Code,2023-09-29T19:58:24+00:00,2023-09-29T19:58:24+00:00,1,198,171,369,27,7,1,direct_commit,43.35,1,Vladimir Guevara,6
TLNTD-468,🔀 TLNTD-468 - Adds back button to Login Page when come from fresh install,Swift Code,2023-09-29T19:53:45+00:00,2023-09-29T19:53:45+00:00,1,29,13,42,16,9,1,direct_commit,22.55,1,Vladimir Guevara,6
TLNTD-288,🔀 TLNTD-288 - Fix message in blocked identity validation dialog (pull request #81),Onboarding,2023-09-28T20:11:25+00:00,2023-09-28T20:11:25+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Rodrigo Mejia,6
TLNTD-311,🔀 TLNTD-311 - Fixes AFP authorization UI and improves underlying logic (pull request #79),Core,2023-09-27T23:47:38+00:00,2023-09-27T23:47:38+00:00,1,655,274,929,381,20,1,direct_commit,120.2,1,Rodrigo Mejia,6
TLNTD-345,🔀 TLNTD-345 - Sets right color for score selection,Swift Code,2023-09-27T19:31:54+00:00,2023-09-27T19:31:54+00:00,1,246,207,453,39,7,1,direct_commit,49.95,1,Vladimir Guevara,6
TLNTD-45,🔀 TLNTD-45 - Fix input validations in salary information form (pull request #74),Swift Code,2023-09-26T20:52:16+00:00,2023-09-26T20:52:16+00:00,1,845,748,1593,97,9,1,direct_commit,140.9,1,Rodrigo Mejia,6
TLNTD-44,🔀 TLNTD-44/TLNTD-546 - Corrects handling of education levels,Swift Code,2023-09-26T20:48:22+00:00,2023-09-26T20:48:22+00:00,1,192,216,408,-24,5,1,direct_commit,41.0,1,Vladimir Guevara,6
TLNTD-546,🔀 TLNTD-44/TLNTD-546 - Corrects handling of education levels,Swift Code,2023-09-26T20:48:22+00:00,2023-09-26T20:48:22+00:00,1,192,216,408,-24,5,1,direct_commit,41.0,1,Vladimir Guevara,6
TLNTD-653,🔀 TLNTD-653 - Fix app navigation routes and map coordinates. (pull request #72),Navigation,2023-09-22T21:52:18+00:00,2023-09-22T21:52:18+00:00,1,31,25,56,6,3,1,direct_commit,11.35,1,Emely Melgar,6
TLNTD-517,🔀 TLNTD-517 - Include guide pages to request Credolab permissions. (pull request #69),DesignSystem,2023-09-22T14:29:10+00:00,2023-09-22T14:55:17+00:00,1,591,44,635,547,31,2,direct_commit,125.3,1,Emely Melgar,6
TLNTD-275,🔀 TLNTD-275 - Improves code style on Credit Card Offert View,Swift Code,2023-09-21T23:01:58+00:00,2023-09-21T23:01:58+00:00,1,188,330,518,-142,1,1,direct_commit,38.3,1,Vladimir Guevara,6
TLNTD-638,🔀 TLNTD-638 - Fix the Google Maps logic on the onboarding maps (pull request #68),Components,2023-09-21T22:31:00+00:00,2023-09-21T22:31:00+00:00,1,856,443,1299,413,10,1,direct_commit,128.75,1,Josseh Blanco,6
TLNTD-341,🔀 TLNTD-341 - Sets DUI address coordinate TLNTD-341 - Sets DUI address coordinates,Swift Code,2023-09-20T15:24:07+00:00,2023-09-21T00:21:29+00:00,1,386,153,539,233,8,3,direct_commit,65.25,1,Vladimir Guevara,6
TLNTD-369,🔀 TLNTD-369 - Add the popups for the biometry error cases (pull request #65),Components,2023-09-19T21:31:51+00:00,2023-09-19T21:31:51+00:00,1,119,24,143,95,8,1,direct_commit,30.1,1,Josseh Blanco,6
TLNTD-333,🔀 TLNTD-333 - Remove address validation alert. (pull request #62),Onboarding,2023-09-19T21:22:34+00:00,2023-09-19T21:22:34+00:00,1,0,26,26,-26,3,1,direct_commit,8.3,1,Emely Melgar,6
TLNTD-565,🔀 TLNTD-565 - Adds new phone number validation (pull request #64),DesignSystem,2023-09-13T21:40:53+00:00,2023-09-18T15:16:09+00:00,4,74,22,96,52,7,2,direct_commit,24.5,2,Vladimir Guevara; Josseh Blanco,48
TLNTD-562,🔀 TLNTD-562 - Fix infinite loop when returning to the view from the OTP screen (pull request #63),Onboarding,2023-09-14T23:41:38+00:00,2023-09-14T23:41:38+00:00,1,11,6,17,5,2,1,direct_commit,6.4,1,Josseh Blanco,6
TLNTD-420,🔀 TLNTD-420 - Fix QA defects and missing features for the address map view (pull request #61),Core,2023-09-07T17:31:52+00:00,2023-09-14T15:29:36+00:00,6,2234,1444,3678,790,33,2,direct_commit,363.6,2,Josseh Blanco; Rodrigo Mejia,72
TLNTD-566,🔀 TLNTD-566/TLNTD-565 - Send the OTP resend popup only when the code has been successfully resent (pull request #60),DesignSystem,2023-09-13T21:40:53+00:00,2023-09-13T21:40:53+00:00,1,71,21,92,50,6,1,direct_commit,21.15,1,Josseh Blanco,6
TLNTD-564,🔀 TLNTD-564 - Remove unnecessary symbol. (pull request #59),Core,2023-09-12T15:26:55+00:00,2023-09-12T15:26:55+00:00,1,1,2,3,-1,1,1,direct_commit,3.2,1,Emely Melgar,6
TLNTD-495,🔀 TLNTD-495 - Refactor credolab implementation. (pull request #58),Core,2023-09-09T04:23:52+00:00,2023-09-09T04:23:52+00:00,1,298,25,323,273,12,1,direct_commit,56.05,1,Emely Melgar,6
TLNTD-509,🔀 TLNTD-509 - Refactors Twilio Chat Support flow (pull request #55),Core,2023-09-09T03:43:29+00:00,2023-09-09T03:43:29+00:00,1,1175,139,1314,1036,27,1,direct_commit,179.45,1,Rodrigo Mejia,6
TLNTD-556,🔀 TLNTD-556 - Use the correct popup type for the existing user alert (pull request #54),Onboarding,2023-09-09T02:44:54+00:00,2023-09-09T02:44:54+00:00,1,1,1,2,0,1,1,direct_commit,3.15,1,Josseh Blanco,6
TLNTD-337,🔀 TLNTD-337 & TLNTD-417 -  Add new phone/email OTP Flow (pull request #57),Swift Code,2023-09-09T02:43:39+00:00,2023-09-09T02:43:39+00:00,1,1266,590,1856,676,43,1,direct_commit,243.1,1,Josseh Blanco,6
TLNTD-417,🔀 TLNTD-337 & TLNTD-417 -  Add new phone/email OTP Flow (pull request #57),Swift Code,2023-09-09T02:43:39+00:00,2023-09-09T02:43:39+00:00,1,1266,590,1856,676,43,1,direct_commit,243.1,1,Josseh Blanco,6
TLNTD-538,🔀 TLNTD-538 - Include generic checkpoint  (pull request #53),Core,2023-09-07T15:54:00+00:00,2023-09-07T15:54:00+00:00,1,1556,375,1931,1181,49,1,direct_commit,273.35,1,Emely Melgar,6
TLNTD-512,🔀 TLNTD-512 - Refactors Twilio Chat Service (pull request #49),Core,2023-09-05T00:06:24+00:00,2023-09-05T00:06:24+00:00,1,330,0,330,330,5,1,direct_commit,44.0,1,Rodrigo Mejia,6
TLNTD-508,💚  TLNTD-508 - Fix sonar reports (pull request #48),Swift Code,2023-08-29T23:39:55+00:00,2023-08-31T14:52:18+00:00,1,877,320,1197,557,18,2,direct_commit,141.7,1,Emely Melgar,6
TLNTD-541,🔀 TLNTD-541 - Improve the logic to handle all possible CCApplications cases (pull request #50),Core,2023-08-29T16:46:59+00:00,2023-08-31T01:00:07+00:00,1,126,144,270,-18,10,2,direct_commit,41.8,1,Josseh Blanco,6
TLNTD-422,🔀 TLNTD-422 - Fix incomplete merge (pull request #45),Main,2023-08-26T02:14:07+00:00,2023-08-26T02:45:02+00:00,1,282,189,471,93,11,2,direct_commit,61.65,2,Vladimir Guevara; Josseh Blanco,12
TLNTD-367,🔀 TLNTD-367 - Add the correct routes for users with existing applications (pull request #44),Components,2023-08-15T16:34:09+00:00,2023-08-26T01:10:19+00:00,10,2377,464,2841,1913,57,4,direct_commit,378.9,1,Josseh Blanco,60
TLNTD-477,🔀 TLNTD-477 - Refactor FacePhi IDScanner and SelfieScanner (pull request #43),Identity,2023-08-25T23:31:34+00:00,2023-08-25T23:31:34+00:00,1,2574,980,3554,1594,59,1,direct_commit,425.4,1,Emely Melgar,6
TLNTD-514,🔀 TLNTD-514 - Route the user to the phone OTP Screen after saving/confirming their address (pull request #39),Components,2023-08-21T17:35:53+00:00,2023-08-21T17:35:53+00:00,1,159,105,264,54,9,1,direct_commit,40.15,1,Josseh Blanco,6
TLNTD-339,🔀 TLNTD-339 - Reallocates ResumenDatos files (pull request #36),Onboarding,2023-08-17T22:44:20+00:00,2023-08-17T22:44:20+00:00,1,240,352,592,-112,9,1,direct_commit,60.6,1,Vladimir Guevara,6
TLNTD-490,🔀 TLNTD-490 - Corrects bad tracked events (pull request #34),Swift Code,2023-08-10T15:29:47+00:00,2023-08-10T15:29:47+00:00,1,2,4,6,-2,2,1,direct_commit,5.4,1,Vladimir Guevara,6
TLNTD-462,🔀 TLNTD-462 - Set up AnalyticsManager and Deprecates (pull request #33),Swift Code,2023-08-09T22:25:22+00:00,2023-08-10T14:59:00+00:00,1,690,247,937,443,41,2,direct_commit,165.35,1,Vladimir Guevara,6
TLNTD-489,Merged in feature/TLNTD-489 (pull request #27),Swift Code,2023-08-08T00:10:22+00:00,2023-08-08T00:10:22+00:00,1,265,112,377,153,13,1,direct_commit,59.1,1,Rodrigo Mejia,6
TLNTD-496,🚀 TLNTD-496 - Bump version number to 1.1.1 (9),Build System,2023-08-04T23:33:42+00:00,2023-08-04T18:32:04-06:00,1,33,22,55,11,4,3,direct_commit,15.4,2,jblanco-applaudostudios; Josseh Blanco,12
TLNTD-365,🔀 TLNTD-365 - Update the login flow for registered users (pull request #26),Swift Code,2023-07-28T21:38:41+00:00,2023-07-28T21:38:41+00:00,1,836,429,1265,407,24,1,direct_commit,154.05,1,Emely Melgar,6
TLNTD-474,🔀 TLNTD-474 - Include OneApp Design System. (pull request #19),Other,2023-07-21T23:27:37+00:00,2023-07-21T23:27:37+00:00,1,1305,47,1352,1258,267,1,direct_commit,667.85,1,Emely Melgar,6
TLNTD-12,🔀 TLNTD-12 - Use Firebase's RemoteConfig to enable/disable screen capture in the app (pull request #22),Swift Code,2023-07-21T22:43:04+00:00,2023-07-21T22:43:04+00:00,1,429,283,712,146,26,1,direct_commit,110.05,1,Josseh Blanco,6
TLNTD-451,🔀 TLNTD-451 - Remove DUI validation allow user to continue flow. (pull request #21),Swift Code,2023-07-21T15:24:16+00:00,2023-07-21T15:24:16+00:00,1,393,345,738,48,28,1,direct_commit,113.55,1,Emely Melgar,6
TLNTD-475,🔀 TLNTD-475 - Include project guidelines. (pull request #20),Other,2023-07-18T19:39:24+00:00,2023-07-18T19:39:24+00:00,1,144,4,148,140,9,1,direct_commit,33.6,1,Emely Melgar,6
TLNTD-473,🔀 TLNTD-473 - Fix UI issues in the DUI and guest view. (pull request #18),Swift Code,2023-07-15T00:38:59+00:00,2023-07-15T00:38:59+00:00,1,11,5,16,6,5,1,direct_commit,12.35,1,Emely Melgar,6
TLNTD-210,🔀 TLNTD-210 - Set up Fastlane for local builds (pull request #17),Other,2023-07-14T17:41:35+00:00,2023-07-14T17:41:35+00:00,1,288,242,530,46,16,1,direct_commit,73.9,1,Josseh Blanco,6
TLNTD-437,🔀 TLNTD-437 - Include onboarding flow for new Customer without request. (pull request #16),Other,2023-07-14T16:25:59+00:00,2023-07-14T16:25:59+00:00,1,1653,318,1971,1335,99,1,direct_commit,380.2,1,Emely Melgar,6
TLNTD-209,🔀 TLNTD-209 - Manage the app's certificates and profiles with Fastlane Match (pull request #15),Other,2023-07-06T15:28:07+00:00,2023-07-06T15:28:07+00:00,1,98,22,120,76,7,1,direct_commit,25.9,1,Josseh Blanco,6
TLNTD-215,🔀 TLNTD-215 - Refactor the crypto manager class. (pull request #14),Swift Code,2023-07-01T00:09:18+00:00,2023-07-01T00:09:18+00:00,1,166,145,311,21,18,1,direct_commit,60.85,1,Emely Melgar,6
TLNTD-374,🔀 TLNTD-374 - Incorporate the second part of the new Networking Layer. (pull request #9),Swift Code,2023-06-30T20:44:37+00:00,2023-06-30T20:44:37+00:00,1,998,613,1611,385,171,1,direct_commit,473.45,1,Emely Melgar,6
TLNTD-447,🔀 TLNTD-447 - Add a launch screen when the app starts,Build System,2023-06-30T15:12:00+00:00,2023-06-30T15:12:00+00:00,1,137,84,221,53,4,1,direct_commit,26.9,1,Josseh Blanco,6
TLNTD-249,🔀 TLNTD-249 - Fix the app crash at launch,Build System,2023-06-29T11:10:13-06:00,2023-06-30T00:31:49+00:00,1,243,140,383,103,4,3,direct_commit,42.3,2,jblanco-applaudostudios; Josseh Blanco,12
TLNTD-201,🔀 TLNTD-201 - Add support for showing coordinators in the existing navigation module,Swift Code,2023-06-29T22:40:04+00:00,2023-06-29T22:40:04+00:00,1,2048,1963,4011,85,231,1,direct_commit,765.95,1,Josseh Blanco,6
TLNTD-216,🔀 TLNTD-216 - Remove the hard-coded public key string from the code (pull request #8),Configuration,2023-06-22T15:31:10+00:00,2023-06-22T15:31:10+00:00,1,29,34,63,-5,5,1,direct_commit,15.6,1,Josseh Blanco,6
TLNTD-213,Merged in task/TLNTD-213-2 (pull request #7),Other,2023-06-19T16:11:43+00:00,2023-06-19T16:11:43+00:00,1,38,44,82,-6,6,1,direct_commit,19.0,1,Josseh Blanco,6
TLNTD-207,Merged in task/TLNTD-207-2.2 (pull request #6),Other,2023-06-14T17:48:05-06:00,2023-06-17T01:42:46+00:00,2,29640,2327,31967,27313,609,21,direct_commit,4319.35,3,jblanco-applaudostudios; Josseh Blanco; Emely Melgar,36
TLNTD-200,Merged in task/TLNTD-200 (pull request #5),Swift Code,2023-06-06T14:15:22-06:00,2023-06-16T23:17:20+00:00,10,1757,235,1992,1522,44,19,direct_commit,294.45,1,Emely Melgar,60
TLNTD-206,"🔧 TLNTD-206 - Setup the app's environments (Dev, QA, UAT, Prod)",Build System,2023-06-07T12:43:19-06:00,2023-06-15T15:14:49+00:00,7,3455,10259,13714,-6804,39,26,direct_commit,962.45,2,jblanco-applaudostudios; Josseh Blanco,84
OBS-42,"Merged PR 2385: :bug:  OBS-42, 43 , 14 ,36, 29 has been completed",Swift Code,2023-04-21T13:13:49-06:00,2023-04-21T19:15:25+00:00,1,220,231,451,-11,16,2,direct_commit,67.55,2,bryan garcia; Bryan Alexis García García,12
OBS-14,OBS-14 has been done,Swift Code,2023-04-21T09:28:37-06:00,2023-04-21T09:28:37-06:00,1,7,22,29,-15,5,1,direct_commit,12.8,1,bryan garcia,6
OBS-39,Merged PR 2380: :bug: transactions  OBS-39 AND OBS-40 has been completed,Swift Code,2023-04-19T17:33:15-06:00,2023-04-19T23:39:49+00:00,1,306,120,426,186,4,2,direct_commit,46.6,2,bryan garcia; Bryan Alexis García García,12
OBS-40,Merged PR 2380: :bug: transactions  OBS-39 AND OBS-40 has been completed,Swift Code,2023-04-19T17:33:15-06:00,2023-04-19T23:39:49+00:00,1,306,120,426,186,4,2,direct_commit,46.6,2,bryan garcia; Bryan Alexis García García,12
OBS-26,:bug: OBS-26 completed,Swift Code,2023-04-17T11:31:29-06:00,2023-04-17T11:31:29-06:00,1,9,11,20,-2,1,1,direct_commit,4.45,1,bryan garcia,6
OBS-04,:bug: OBS-04 IS COMPLETED,Build System,2023-04-14T00:19:03-06:00,2023-04-14T00:19:03-06:00,1,88,58,146,30,17,1,direct_commit,46.7,1,bryan garcia,6
OBS-06,Merged PR 2354: :bug: payment card option HU OBS-06-07 has been updated,Swift Code,2023-04-13T13:59:45-06:00,2023-04-13T20:01:56+00:00,1,284,82,366,202,7,2,direct_commit,48.5,2,bryan garcia; Bryan Alexis García García,12
