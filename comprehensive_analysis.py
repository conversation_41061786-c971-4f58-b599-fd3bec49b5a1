#!/usr/bin/env python3
"""
Comprehensive Analysis of the Complete Feature Dataset
Analyzes all 756 features for realistic estimation insights
"""

import csv
import pandas as pd
from collections import Counter
import numpy as np
from datetime import datetime

def analyze_comprehensive_features():
    """Analyze the comprehensive feature dataset"""
    print("📊 Analyzing Comprehensive Feature Dataset")
    print("=" * 50)
    
    # Load the comprehensive dataset
    try:
        df = pd.read_csv('comprehensive_feature_metrics.csv')
        print(f"✅ Loaded {len(df)} features")
    except FileNotFoundError:
        print("❌ comprehensive_feature_metrics.csv not found")
        return
    
    # Basic statistics
    print(f"\n📈 Basic Statistics:")
    print(f"  - Total Features: {len(df)}")
    print(f"  - Date Range: {df['development_start_date'].min()} to {df['development_end_date'].max()}")
    
    # Development time analysis
    dev_days = df['total_dev_days'].astype(int)
    print(f"\n⏱️ Development Time Analysis:")
    print(f"  - Average: {dev_days.mean():.1f} days")
    print(f"  - Median: {dev_days.median():.1f} days")
    print(f"  - Min: {dev_days.min()} days")
    print(f"  - Max: {dev_days.max()} days")
    print(f"  - 75th percentile: {dev_days.quantile(0.75):.1f} days")
    print(f"  - 90th percentile: {dev_days.quantile(0.90):.1f} days")
    
    # Feature size distribution
    small_features = len(df[dev_days <= 3])
    medium_features = len(df[(dev_days > 3) & (dev_days <= 14)])
    large_features = len(df[dev_days > 14])
    
    print(f"\n📏 Feature Size Distribution:")
    print(f"  - Small (≤3 days): {small_features} ({small_features/len(df)*100:.1f}%)")
    print(f"  - Medium (4-14 days): {medium_features} ({medium_features/len(df)*100:.1f}%)")
    print(f"  - Large (>14 days): {large_features} ({large_features/len(df)*100:.1f}%)")
    
    # Module analysis
    modules = df['module_component'].value_counts()
    print(f"\n🏗️ Module Distribution (Top 10):")
    for i, (module, count) in enumerate(modules.head(10).items()):
        print(f"  {i+1}. {module}: {count} features ({count/len(df)*100:.1f}%)")
    
    # Team size analysis
    team_sizes = df['team_size'].astype(int)
    print(f"\n👥 Team Size Analysis:")
    print(f"  - Average: {team_sizes.mean():.1f} developers")
    print(f"  - Most common: {team_sizes.mode().iloc[0]} developer(s)")
    print(f"  - Single developer: {len(df[team_sizes == 1])} features ({len(df[team_sizes == 1])/len(df)*100:.1f}%)")
    print(f"  - Multiple developers: {len(df[team_sizes > 1])} features ({len(df[team_sizes > 1])/len(df)*100:.1f}%)")
    
    # Complexity analysis
    complexity = df['complexity_score'].astype(float)
    print(f"\n🧮 Complexity Analysis:")
    print(f"  - Average: {complexity.mean():.1f}")
    print(f"  - Median: {complexity.median():.1f}")
    print(f"  - Low complexity (<50): {len(df[complexity < 50])} features")
    print(f"  - Medium complexity (50-200): {len(df[(complexity >= 50) & (complexity <= 200)])} features")
    print(f"  - High complexity (>200): {len(df[complexity > 200])} features")
    
    # Lines of code analysis
    lines_modified = df['lines_modified'].astype(int)
    print(f"\n💻 Code Changes Analysis:")
    print(f"  - Average lines modified: {lines_modified.mean():.0f}")
    print(f"  - Median lines modified: {lines_modified.median():.0f}")
    print(f"  - Small changes (<100 lines): {len(df[lines_modified < 100])} features")
    print(f"  - Medium changes (100-1000 lines): {len(df[(lines_modified >= 100) & (lines_modified <= 1000)])} features")
    print(f"  - Large changes (>1000 lines): {len(df[lines_modified > 1000])} features")
    
    # Top contributors
    all_developers = []
    for dev_list in df['developer_names']:
        if pd.notna(dev_list):
            all_developers.extend(dev_list.split('; '))
    
    dev_counts = Counter(all_developers)
    print(f"\n🏆 Top Contributors:")
    for i, (dev, count) in enumerate(dev_counts.most_common(10)):
        print(f"  {i+1}. {dev}: {count} features")
    
    # Recent activity (last 6 months)
    df['start_date'] = pd.to_datetime(df['development_start_date'])
    recent_cutoff = datetime.now() - pd.Timedelta(days=180)
    recent_features = df[df['start_date'] >= recent_cutoff]
    
    print(f"\n📅 Recent Activity (Last 6 months):")
    print(f"  - Recent features: {len(recent_features)} ({len(recent_features)/len(df)*100:.1f}%)")
    if len(recent_features) > 0:
        recent_dev_days = recent_features['total_dev_days'].astype(int)
        print(f"  - Recent average dev time: {recent_dev_days.mean():.1f} days")
        print(f"  - Recent median dev time: {recent_dev_days.median():.1f} days")
    
    # Generate estimation recommendations
    generate_realistic_recommendations(df)

def generate_realistic_recommendations(df):
    """Generate realistic estimation recommendations based on complete data"""
    print(f"\n🎯 Realistic Estimation Recommendations")
    print("=" * 50)
    
    dev_days = df['total_dev_days'].astype(int)
    
    # Percentile-based estimates
    p25 = dev_days.quantile(0.25)
    p50 = dev_days.quantile(0.50)
    p75 = dev_days.quantile(0.75)
    p90 = dev_days.quantile(0.90)
    
    print(f"📊 Percentile-Based Estimates:")
    print(f"  - 25th percentile (Optimistic): {p25:.1f} days")
    print(f"  - 50th percentile (Typical): {p50:.1f} days")
    print(f"  - 75th percentile (Conservative): {p75:.1f} days")
    print(f"  - 90th percentile (High Risk): {p90:.1f} days")
    
    # Module-specific estimates
    print(f"\n🏗️ Module-Specific Estimates:")
    for module in df['module_component'].value_counts().head(8).index:
        module_data = df[df['module_component'] == module]['total_dev_days'].astype(int)
        if len(module_data) >= 5:  # Only modules with enough data
            print(f"  - {module}: {module_data.median():.1f} days median ({len(module_data)} features)")
    
    # Team size impact
    print(f"\n👥 Team Size Impact:")
    for team_size in sorted(df['team_size'].unique()):
        if team_size <= 5:  # Focus on common team sizes
            team_data = df[df['team_size'] == team_size]['total_dev_days'].astype(int)
            if len(team_data) >= 10:
                print(f"  - {int(team_size)} developer(s): {team_data.median():.1f} days median ({len(team_data)} features)")
    
    # Complexity-based estimates
    complexity = df['complexity_score'].astype(float)
    print(f"\n🧮 Complexity-Based Estimates:")
    
    low_complexity = df[complexity < 50]['total_dev_days'].astype(int)
    medium_complexity = df[(complexity >= 50) & (complexity <= 200)]['total_dev_days'].astype(int)
    high_complexity = df[complexity > 200]['total_dev_days'].astype(int)
    
    if len(low_complexity) > 0:
        print(f"  - Low complexity (<50): {low_complexity.median():.1f} days median")
    if len(medium_complexity) > 0:
        print(f"  - Medium complexity (50-200): {medium_complexity.median():.1f} days median")
    if len(high_complexity) > 0:
        print(f"  - High complexity (>200): {high_complexity.median():.1f} days median")
    
    # Practical recommendations
    print(f"\n💡 Practical Recommendations:")
    print(f"  - For quick estimates: Use {p50:.0f} days as baseline")
    print(f"  - For planning: Use {p75:.0f} days for buffer")
    print(f"  - For risk assessment: Features >{p90:.0f} days need special attention")
    print(f"  - Most features ({(len(df[dev_days <= p75])/len(df)*100):.0f}%) complete within {p75:.0f} days")

def main():
    """Main analysis function"""
    analyze_comprehensive_features()

if __name__ == "__main__":
    main()
