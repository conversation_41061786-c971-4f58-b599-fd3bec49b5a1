# Repository Analysis Summary

## Overview
- **Total Features Analyzed**: 62
- **Total Commits Analyzed**: 4472
- **Analysis Date**: 2025-10-07 10:32:55

## Development Metrics
- **Average Development Time**: 860.4 days
- **Average Complexity Score**: 111129.2
- **Average Team Size**: 20.9 developers

## Module Distribution
- **Other**: 42 features (67.7%)
- **Unknown**: 20 features (32.3%)

## Top Contributors
- **<PERSON><PERSON><PERSON>**: 47 features
- **<PERSON>**: 47 features
- **Em<PERSON>**: 45 features
- **<PERSON>**: 44 features
- **<PERSON>**: 44 features
- **<PERSON>**: 43 features
- **<PERSON>**: 43 features
- **<PERSON>**: 42 features
- **<PERSON>**: 42 features
- **bryan garcia**: 42 features

## Feature Size Distribution
- **Small Features** (≤3 days): 20 (32.3%)
- **Medium Features** (4-14 days): 0 (0.0%)
- **Large Features** (>14 days): 42 (67.7%)

## Repository Structure
- **Primary Language**: Swift (iOS)
- **Architecture**: Modular SwiftUI/UIKit
- **Main Modules**: Other, Unknown
