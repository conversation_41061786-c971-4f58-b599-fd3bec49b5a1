#!/usr/bin/env python3
"""
Comprehensive Feature Extractor
Extracts ALL features from the repository, including those without dedicated branches
"""

import re
import csv
import subprocess
import json
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import os
import sys

class ComprehensiveFeatureExtractor:
    def __init__(self, repo_path="."):
        self.repo_path = repo_path
        self.jira_pattern = re.compile(r'(?i)([A-Z]{2,10}-\d+)')
        self.features = {}
        self.total_commits_analyzed = 0
        
    def run_git_command(self, command):
        """Execute git command and return output"""
        try:
            result = subprocess.run(
                command, shell=True, cwd=self.repo_path,
                capture_output=True, text=True, check=True
            )
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            print(f"Git command failed: {command}")
            print(f"Error: {e.stderr}")
            return ""
    
    def extract_all_jira_commits(self):
        """Extract ALL commits that contain Jira tickets"""
        print("🔍 Extracting all commits with Jira tickets...")
        
        # Get all commits with Jira patterns in commit messages
        all_commits = self.run_git_command(
            "git log --all --pretty=format:'%H|%ai|%an|%ae|%s' --grep='[A-Z]\\+-[0-9]\\+' --regexp-ignore-case"
        )
        
        jira_commits = []
        for line in all_commits.split('\n'):
            if line.strip():
                parts = line.split('|')
                if len(parts) >= 5:
                    commit_hash = parts[0]
                    commit_date = parts[1]
                    author = parts[2]
                    email = parts[3]
                    message = parts[4]
                    
                    # Extract Jira tickets from message
                    jira_matches = self.jira_pattern.findall(message)
                    for jira_ticket in jira_matches:
                        jira_commits.append({
                            'hash': commit_hash,
                            'date': commit_date,
                            'author': author,
                            'email': email,
                            'message': message,
                            'jira_ticket': jira_ticket.upper()
                        })
        
        print(f"📊 Found {len(jira_commits)} commits with Jira tickets")
        return jira_commits
    
    def group_commits_by_jira(self, jira_commits):
        """Group commits by Jira ticket"""
        print("📋 Grouping commits by Jira ticket...")
        
        jira_groups = defaultdict(list)
        for commit in jira_commits:
            jira_groups[commit['jira_ticket']].append(commit)
        
        print(f"🎯 Found {len(jira_groups)} unique Jira tickets")
        return jira_groups
    
    def get_commit_statistics(self, commit_hash):
        """Get file changes for a specific commit"""
        try:
            stats_output = self.run_git_command(f"git show --numstat {commit_hash}")
            
            lines_added = 0
            lines_deleted = 0
            files_changed = set()
            
            for line in stats_output.split('\n'):
                if line.strip() and '\t' in line:
                    parts = line.split('\t')
                    if len(parts) >= 3:
                        try:
                            added = int(parts[0]) if parts[0] != '-' else 0
                            deleted = int(parts[1]) if parts[1] != '-' else 0
                            filename = parts[2]
                            
                            lines_added += added
                            lines_deleted += deleted
                            files_changed.add(filename)
                        except ValueError:
                            continue
            
            return {
                'lines_added': lines_added,
                'lines_deleted': lines_deleted,
                'files_changed': list(files_changed)
            }
        except Exception as e:
            return {
                'lines_added': 0,
                'lines_deleted': 0,
                'files_changed': []
            }
    
    def identify_primary_module(self, changed_files):
        """Identify the primary module based on changed files"""
        module_counts = Counter()
        
        for file_path in changed_files:
            if 'Atlantida/Source/Modules/' in file_path:
                # Extract module name
                parts = file_path.split('Atlantida/Source/Modules/')
                if len(parts) > 1:
                    module = parts[1].split('/')[0]
                    module_counts[module] += 1
            elif 'Atlantida/Source/Components/' in file_path:
                module_counts['Components'] += 1
            elif 'Atlantida/Source/Core/' in file_path:
                module_counts['Core'] += 1
            elif 'Atlantida/Source/DesignSystem/' in file_path:
                module_counts['DesignSystem'] += 1
            elif '.swift' in file_path:
                module_counts['Swift Code'] += 1
            elif any(config in file_path for config in ['.yml', '.yaml', '.json', '.plist']):
                module_counts['Configuration'] += 1
            elif any(build in file_path for build in ['Podfile', 'Package.swift', '.xcodeproj']):
                module_counts['Build System'] += 1
            else:
                module_counts['Other'] += 1
        
        # Get the most affected module
        return module_counts.most_common(1)[0][0] if module_counts else 'Unknown'
    
    def analyze_jira_feature(self, jira_ticket, commits):
        """Analyze a complete Jira feature from all its commits"""
        if not commits:
            return None
        
        # Sort commits by date
        commits.sort(key=lambda x: datetime.fromisoformat(x['date'].replace('Z', '+00:00')))
        
        # Calculate timeline
        start_date = datetime.fromisoformat(commits[0]['date'].replace('Z', '+00:00'))
        end_date = datetime.fromisoformat(commits[-1]['date'].replace('Z', '+00:00'))
        total_dev_days = max((end_date - start_date).days, 1)
        
        # Aggregate statistics from all commits
        total_lines_added = 0
        total_lines_deleted = 0
        all_files_changed = set()
        authors = set()
        
        for commit in commits:
            stats = self.get_commit_statistics(commit['hash'])
            total_lines_added += stats['lines_added']
            total_lines_deleted += stats['lines_deleted']
            all_files_changed.update(stats['files_changed'])
            authors.add(commit['author'])
        
        # Identify primary module
        module = self.identify_primary_module(list(all_files_changed))
        
        # Calculate complexity score
        complexity_score = (
            total_lines_added * 0.1 +
            total_lines_deleted * 0.05 +
            len(all_files_changed) * 2 +
            len(commits) * 1
        )
        
        # Get feature description from latest commit
        feature_description = commits[-1]['message']
        
        # Determine branch type
        branch_type = 'direct_commit'  # Default for commits not on feature branches
        
        return {
            'jira_ticket': jira_ticket,
            'feature_description': feature_description,
            'module_component': module,
            'development_start_date': start_date.isoformat(),
            'development_end_date': end_date.isoformat(),
            'total_dev_days': total_dev_days,
            'lines_added': total_lines_added,
            'lines_deleted': total_lines_deleted,
            'lines_modified': total_lines_added + total_lines_deleted,
            'total_line_count': total_lines_added - total_lines_deleted,
            'files_changed': len(all_files_changed),
            'commits_count': len(commits),
            'branch_name': branch_type,
            'complexity_score': round(complexity_score, 2),
            'team_size': len(authors),
            'developer_names': list(authors),
            'productivity_factor': round(6 * (len(authors) * total_dev_days), 2)
        }
    
    def extract_features_from_commit_patterns(self):
        """Extract features based on commit message patterns (even without Jira tickets)"""
        print("🔍 Extracting features from commit patterns...")
        
        # Look for feature-like commit patterns
        feature_patterns = [
            r'(?i)(feat|feature|add|implement|create)[:|\s]',
            r'(?i)✨',  # Sparkles emoji for new features
            r'(?i)(new|added|implement)',
            r'(?i)(refactor|♻️)',  # Refactor commits
            r'(?i)(fix|bug|🐛)',   # Bug fixes
        ]
        
        pattern_commits = []
        for pattern in feature_patterns:
            commits_output = self.run_git_command(
                f"git log --all --pretty=format:'%H|%ai|%an|%ae|%s' --grep='{pattern}' --regexp-ignore-case"
            )
            
            for line in commits_output.split('\n'):
                if line.strip():
                    parts = line.split('|')
                    if len(parts) >= 5:
                        pattern_commits.append({
                            'hash': parts[0],
                            'date': parts[1],
                            'author': parts[2],
                            'email': parts[3],
                            'message': parts[4],
                            'pattern': pattern
                        })
        
        print(f"📊 Found {len(pattern_commits)} feature-pattern commits")
        return pattern_commits
    
    def analyze_all_features(self):
        """Main method to extract ALL features from the repository"""
        print("🚀 Starting Comprehensive Feature Extraction")
        print("=" * 60)
        
        # 1. Extract all Jira-based features
        jira_commits = self.extract_all_jira_commits()
        jira_groups = self.group_commits_by_jira(jira_commits)
        
        features = []
        
        # Process each Jira ticket
        for i, (jira_ticket, commits) in enumerate(jira_groups.items(), 1):
            if i % 50 == 0:
                print(f"  Processing Jira tickets: {i}/{len(jira_groups)}")
            
            feature = self.analyze_jira_feature(jira_ticket, commits)
            if feature:
                features.append(feature)
        
        print(f"✅ Processed {len(features)} Jira-based features")
        
        # 2. Extract additional features from commit patterns (for non-Jira features)
        pattern_commits = self.extract_features_from_commit_patterns()
        
        # Group pattern commits by similarity (basic grouping by date and author)
        pattern_groups = defaultdict(list)
        for commit in pattern_commits:
            # Skip if already covered by Jira analysis
            if any(self.jira_pattern.search(commit['message']) for _ in [1]):
                continue
            
            # Group by author and date proximity (same day)
            date_key = commit['date'][:10]  # YYYY-MM-DD
            author_key = commit['author']
            group_key = f"{author_key}_{date_key}"
            pattern_groups[group_key].append(commit)
        
        # Process pattern-based features
        pattern_features = 0
        for group_key, commits in pattern_groups.items():
            if len(commits) >= 1:  # At least 1 commit to be considered a feature
                feature = self.analyze_pattern_feature(group_key, commits)
                if feature:
                    features.append(feature)
                    pattern_features += 1
        
        print(f"✅ Processed {pattern_features} pattern-based features")
        
        self.features = features
        print(f"🎯 Total features extracted: {len(features)}")
        
        return features
    
    def analyze_pattern_feature(self, group_key, commits):
        """Analyze a feature based on commit patterns"""
        if not commits:
            return None
        
        # Sort commits by date
        commits.sort(key=lambda x: datetime.fromisoformat(x['date'].replace('Z', '+00:00')))
        
        # Calculate timeline
        start_date = datetime.fromisoformat(commits[0]['date'].replace('Z', '+00:00'))
        end_date = datetime.fromisoformat(commits[-1]['date'].replace('Z', '+00:00'))
        total_dev_days = max((end_date - start_date).days, 1)
        
        # Aggregate statistics
        total_lines_added = 0
        total_lines_deleted = 0
        all_files_changed = set()
        authors = set()
        
        for commit in commits:
            stats = self.get_commit_statistics(commit['hash'])
            total_lines_added += stats['lines_added']
            total_lines_deleted += stats['lines_deleted']
            all_files_changed.update(stats['files_changed'])
            authors.add(commit['author'])
        
        # Skip very small changes (likely not real features)
        if total_lines_added + total_lines_deleted < 5:
            return None
        
        # Identify primary module
        module = self.identify_primary_module(list(all_files_changed))
        
        # Calculate complexity score
        complexity_score = (
            total_lines_added * 0.1 +
            total_lines_deleted * 0.05 +
            len(all_files_changed) * 2 +
            len(commits) * 1
        )
        
        # Generate feature ID and description
        feature_id = f"PATTERN-{group_key}"
        feature_description = commits[-1]['message']
        
        return {
            'jira_ticket': feature_id,
            'feature_description': feature_description,
            'module_component': module,
            'development_start_date': start_date.isoformat(),
            'development_end_date': end_date.isoformat(),
            'total_dev_days': total_dev_days,
            'lines_added': total_lines_added,
            'lines_deleted': total_lines_deleted,
            'lines_modified': total_lines_added + total_lines_deleted,
            'total_line_count': total_lines_added - total_lines_deleted,
            'files_changed': len(all_files_changed),
            'commits_count': len(commits),
            'branch_name': 'pattern_based',
            'complexity_score': round(complexity_score, 2),
            'team_size': len(authors),
            'developer_names': list(authors),
            'productivity_factor': round(6 * (len(authors) * total_dev_days), 2)
        }
    
    def export_to_csv(self, filename='comprehensive_feature_metrics.csv'):
        """Export all features to CSV"""
        if not self.features:
            print("No features to export")
            return
        
        fieldnames = [
            'jira_ticket', 'feature_description', 'module_component',
            'development_start_date', 'development_end_date', 'total_dev_days',
            'lines_added', 'lines_deleted', 'lines_modified', 'total_line_count',
            'files_changed', 'commits_count', 'branch_name', 'complexity_score',
            'team_size', 'developer_names', 'productivity_factor'
        ]
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for feature in self.features:
                # Convert developer_names list to string
                feature_copy = feature.copy()
                feature_copy['developer_names'] = '; '.join(feature.get('developer_names', []))
                
                # Ensure all required fields exist
                for field in fieldnames:
                    if field not in feature_copy:
                        feature_copy[field] = ''
                
                writer.writerow(feature_copy)
        
        print(f"✅ Comprehensive CSV exported: {filename} ({len(self.features)} features)")

def main():
    """Main execution function"""
    print("🚀 Starting Comprehensive Feature Extraction")
    print("This will capture ALL features, not just those with dedicated branches")
    print("=" * 70)
    
    extractor = ComprehensiveFeatureExtractor()
    
    # Run complete extraction
    features = extractor.analyze_all_features()
    
    if not features:
        print("❌ No features found to analyze")
        return
    
    # Export results
    extractor.export_to_csv()
    
    print("=" * 70)
    print("✅ Comprehensive extraction complete!")
    print(f"📊 Total features found: {len(features)}")
    print("📁 Generated: comprehensive_feature_metrics.csv")

if __name__ == "__main__":
    main()
