#!/usr/bin/env python3
"""
Simple Comprehensive Analysis of the Complete Feature Dataset
Analyzes all 756 features using only standard library
"""

import csv
from collections import Counter, defaultdict
import statistics
from datetime import datetime

def analyze_comprehensive_features():
    """Analyze the comprehensive feature dataset"""
    print("📊 Analyzing Comprehensive Feature Dataset")
    print("=" * 50)
    
    # Load the comprehensive dataset
    try:
        with open('comprehensive_feature_metrics.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            features = list(reader)
        print(f"✅ Loaded {len(features)} features")
    except FileNotFoundError:
        print("❌ comprehensive_feature_metrics.csv not found")
        return
    
    # Convert numeric fields
    for feature in features:
        try:
            feature['total_dev_days'] = int(feature['total_dev_days'])
            feature['team_size'] = int(feature['team_size'])
            feature['complexity_score'] = float(feature['complexity_score'])
            feature['lines_modified'] = int(feature['lines_modified'])
            feature['commits_count'] = int(feature['commits_count'])
            feature['files_changed'] = int(feature['files_changed'])
        except (ValueError, KeyError):
            continue
    
    # Basic statistics
    print(f"\n📈 Basic Statistics:")
    print(f"  - Total Features: {len(features)}")
    
    # Development time analysis
    dev_days = [f['total_dev_days'] for f in features if 'total_dev_days' in f]
    dev_days.sort()
    
    print(f"\n⏱️ Development Time Analysis:")
    print(f"  - Average: {statistics.mean(dev_days):.1f} days")
    print(f"  - Median: {statistics.median(dev_days):.1f} days")
    print(f"  - Min: {min(dev_days)} days")
    print(f"  - Max: {max(dev_days)} days")
    
    # Calculate percentiles manually
    def percentile(data, p):
        k = (len(data) - 1) * p / 100
        f = int(k)
        c = k - f
        if f == len(data) - 1:
            return data[f]
        return data[f] * (1 - c) + data[f + 1] * c
    
    print(f"  - 75th percentile: {percentile(dev_days, 75):.1f} days")
    print(f"  - 90th percentile: {percentile(dev_days, 90):.1f} days")
    
    # Feature size distribution
    small_features = len([d for d in dev_days if d <= 3])
    medium_features = len([d for d in dev_days if 3 < d <= 14])
    large_features = len([d for d in dev_days if d > 14])
    
    print(f"\n📏 Feature Size Distribution:")
    print(f"  - Small (≤3 days): {small_features} ({small_features/len(dev_days)*100:.1f}%)")
    print(f"  - Medium (4-14 days): {medium_features} ({medium_features/len(dev_days)*100:.1f}%)")
    print(f"  - Large (>14 days): {large_features} ({large_features/len(dev_days)*100:.1f}%)")
    
    # Module analysis
    modules = Counter(f['module_component'] for f in features if f['module_component'])
    print(f"\n🏗️ Module Distribution (Top 10):")
    for i, (module, count) in enumerate(modules.most_common(10)):
        print(f"  {i+1}. {module}: {count} features ({count/len(features)*100:.1f}%)")
    
    # Team size analysis
    team_sizes = [f['team_size'] for f in features if 'team_size' in f]
    team_size_counts = Counter(team_sizes)
    
    print(f"\n👥 Team Size Analysis:")
    print(f"  - Average: {statistics.mean(team_sizes):.1f} developers")
    print(f"  - Most common: {team_size_counts.most_common(1)[0][0]} developer(s)")
    
    single_dev = len([t for t in team_sizes if t == 1])
    multi_dev = len([t for t in team_sizes if t > 1])
    
    print(f"  - Single developer: {single_dev} features ({single_dev/len(team_sizes)*100:.1f}%)")
    print(f"  - Multiple developers: {multi_dev} features ({multi_dev/len(team_sizes)*100:.1f}%)")
    
    # Complexity analysis
    complexity_scores = [f['complexity_score'] for f in features if 'complexity_score' in f]
    
    print(f"\n🧮 Complexity Analysis:")
    print(f"  - Average: {statistics.mean(complexity_scores):.1f}")
    print(f"  - Median: {statistics.median(complexity_scores):.1f}")
    
    low_complexity = len([c for c in complexity_scores if c < 50])
    medium_complexity = len([c for c in complexity_scores if 50 <= c <= 200])
    high_complexity = len([c for c in complexity_scores if c > 200])
    
    print(f"  - Low complexity (<50): {low_complexity} features")
    print(f"  - Medium complexity (50-200): {medium_complexity} features")
    print(f"  - High complexity (>200): {high_complexity} features")
    
    # Lines of code analysis
    lines_modified = [f['lines_modified'] for f in features if 'lines_modified' in f]
    
    print(f"\n💻 Code Changes Analysis:")
    print(f"  - Average lines modified: {statistics.mean(lines_modified):.0f}")
    print(f"  - Median lines modified: {statistics.median(lines_modified):.0f}")
    
    small_changes = len([l for l in lines_modified if l < 100])
    medium_changes = len([l for l in lines_modified if 100 <= l <= 1000])
    large_changes = len([l for l in lines_modified if l > 1000])
    
    print(f"  - Small changes (<100 lines): {small_changes} features")
    print(f"  - Medium changes (100-1000 lines): {medium_changes} features")
    print(f"  - Large changes (>1000 lines): {large_changes} features")
    
    # Top contributors
    all_developers = []
    for feature in features:
        if feature.get('developer_names'):
            all_developers.extend(feature['developer_names'].split('; '))
    
    dev_counts = Counter(all_developers)
    print(f"\n🏆 Top Contributors:")
    for i, (dev, count) in enumerate(dev_counts.most_common(10)):
        print(f"  {i+1}. {dev}: {count} features")
    
    # Generate estimation recommendations
    generate_realistic_recommendations(features, dev_days, modules)

def generate_realistic_recommendations(features, dev_days, modules):
    """Generate realistic estimation recommendations based on complete data"""
    print(f"\n🎯 Realistic Estimation Recommendations")
    print("=" * 50)
    
    # Percentile-based estimates
    def percentile(data, p):
        k = (len(data) - 1) * p / 100
        f = int(k)
        c = k - f
        if f == len(data) - 1:
            return data[f]
        return data[f] * (1 - c) + data[f + 1] * c
    
    p25 = percentile(dev_days, 25)
    p50 = percentile(dev_days, 50)
    p75 = percentile(dev_days, 75)
    p90 = percentile(dev_days, 90)
    
    print(f"📊 Percentile-Based Estimates:")
    print(f"  - 25th percentile (Optimistic): {p25:.1f} days")
    print(f"  - 50th percentile (Typical): {p50:.1f} days")
    print(f"  - 75th percentile (Conservative): {p75:.1f} days")
    print(f"  - 90th percentile (High Risk): {p90:.1f} days")
    
    # Module-specific estimates
    print(f"\n🏗️ Module-Specific Estimates:")
    module_stats = defaultdict(list)
    
    for feature in features:
        if feature.get('module_component') and 'total_dev_days' in feature:
            module_stats[feature['module_component']].append(feature['total_dev_days'])
    
    for module in modules.most_common(8):
        module_name = module[0]
        if len(module_stats[module_name]) >= 5:  # Only modules with enough data
            module_days = sorted(module_stats[module_name])
            median_days = statistics.median(module_days)
            print(f"  - {module_name}: {median_days:.1f} days median ({len(module_days)} features)")
    
    # Team size impact
    print(f"\n👥 Team Size Impact:")
    team_stats = defaultdict(list)
    
    for feature in features:
        if 'team_size' in feature and 'total_dev_days' in feature:
            team_stats[feature['team_size']].append(feature['total_dev_days'])
    
    for team_size in sorted(team_stats.keys()):
        if team_size <= 5 and len(team_stats[team_size]) >= 10:
            team_days = sorted(team_stats[team_size])
            median_days = statistics.median(team_days)
            print(f"  - {team_size} developer(s): {median_days:.1f} days median ({len(team_days)} features)")
    
    # Complexity-based estimates
    print(f"\n🧮 Complexity-Based Estimates:")
    
    low_complexity_days = []
    medium_complexity_days = []
    high_complexity_days = []
    
    for feature in features:
        if 'complexity_score' in feature and 'total_dev_days' in feature:
            complexity = feature['complexity_score']
            days = feature['total_dev_days']
            
            if complexity < 50:
                low_complexity_days.append(days)
            elif 50 <= complexity <= 200:
                medium_complexity_days.append(days)
            else:
                high_complexity_days.append(days)
    
    if low_complexity_days:
        print(f"  - Low complexity (<50): {statistics.median(low_complexity_days):.1f} days median")
    if medium_complexity_days:
        print(f"  - Medium complexity (50-200): {statistics.median(medium_complexity_days):.1f} days median")
    if high_complexity_days:
        print(f"  - High complexity (>200): {statistics.median(high_complexity_days):.1f} days median")
    
    # Practical recommendations
    within_75th = len([d for d in dev_days if d <= p75])
    
    print(f"\n💡 Practical Recommendations:")
    print(f"  - For quick estimates: Use {p50:.0f} days as baseline")
    print(f"  - For planning: Use {p75:.0f} days for buffer")
    print(f"  - For risk assessment: Features >{p90:.0f} days need special attention")
    print(f"  - Most features ({within_75th/len(dev_days)*100:.0f}%) complete within {p75:.0f} days")
    
    # Summary table
    print(f"\n📋 Quick Reference Table:")
    print(f"  Feature Type     | Typical Time | Conservative")
    print(f"  ----------------|-------------|-------------")
    print(f"  Quick Fix       | {p25:.0f} day        | {p50:.0f} days")
    print(f"  Standard Feature| {p50:.0f} days       | {p75:.0f} days")
    print(f"  Complex Feature | {p75:.0f} days       | {p90:.0f} days")

def main():
    """Main analysis function"""
    analyze_comprehensive_features()

if __name__ == "__main__":
    main()
